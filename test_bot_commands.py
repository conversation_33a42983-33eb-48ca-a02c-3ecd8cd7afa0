#!/usr/bin/env python3
"""
Test script to verify Telegram bot commands are working correctly.
This script tests the bot's internal functionality without sending actual Telegram messages.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/morpho_helper')

from bot.services import MarketService
from shared.config import Config

async def test_bot_functionality():
    """Test the bot's core functionality."""
    print("🧪 Testing Morpho Helper Bot Functionality")
    print("=" * 50)
    
    try:
        # Initialize the market service
        market_service = MarketService()
        
        print("✅ Market service initialized successfully")
        
        # Test 1: Status check
        print("\n📊 Testing status functionality...")
        try:
            status_info = await market_service.get_status_info()
            print("✅ Status check successful")
            print(f"Status info preview: {status_info[:200]}...")
        except Exception as e:
            print(f"❌ Status check failed: {e}")
            return False
        
        # Test 2: Market data retrieval
        print("\n📈 Testing market data retrieval...")
        try:
            markets = await market_service._get_markets_with_fallback()
            print(f"✅ Retrieved {len(markets)} markets successfully")
            
            if len(markets) > 0:
                print(f"Sample market: {markets[0].unique_key}")
            else:
                print("⚠️  No markets retrieved")
                return False
                
        except Exception as e:
            print(f"❌ Market data retrieval failed: {e}")
            return False
        
        # Test 3: Market filtering
        print("\n🔍 Testing market filtering...")
        try:
            # Test filtering by loan asset
            filtered_result = await market_service.get_new_whitelisted_markets(loan_asset_filter="USDC")
            print(f"✅ Filtered markets (USDC): {len(filtered_result)} characters in response")

            # Test filtering by collateral asset
            filtered_result = await market_service.get_new_whitelisted_markets(collateral_asset_filter="WETH")
            print(f"✅ Filtered markets (WETH): {len(filtered_result)} characters in response")
            
        except Exception as e:
            print(f"❌ Market filtering failed: {e}")
            return False
        
        # Test 4: Data freshness
        print("\n⏰ Testing data freshness...")
        try:
            # This is tested as part of status, but let's verify the logic
            if "hours old" in status_info or "minutes old" in status_info:
                print("✅ Data freshness calculation working")
            else:
                print("⚠️  Data freshness format unexpected")
                
        except Exception as e:
            print(f"❌ Data freshness test failed: {e}")
            return False
        
        print("\n🎉 All bot functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Bot initialization failed: {e}")
        return False

async def test_connectivity():
    """Test connectivity to all required services."""
    print("\n🔌 Testing Service Connectivity")
    print("=" * 50)
    
    try:
        market_service = MarketService()
        
        # Test connectivity through the main service method
        print("Testing service connectivity...")
        try:
            # This will test the full fallback chain
            markets = await market_service._get_markets_with_fallback()
            print(f"✅ Service connectivity working - {len(markets)} markets retrieved")

            # Test status method which tests all connections
            status = await market_service.get_status_info()
            if "✅" in status:
                print("✅ Status check shows services connected")
            else:
                print("⚠️  Some services may have connectivity issues")

        except Exception as e:
            print(f"❌ Service connectivity failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Connectivity test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Comprehensive Bot Verification")
    print("=" * 60)
    
    # Test connectivity first
    connectivity_ok = await test_connectivity()
    
    # Test bot functionality
    functionality_ok = await test_bot_functionality()
    
    print("\n" + "=" * 60)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 60)
    
    if connectivity_ok and functionality_ok:
        print("🎉 ✅ ALL TESTS PASSED - Bot is fully operational!")
        return 0
    else:
        print("❌ SOME TESTS FAILED - Bot needs attention")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
