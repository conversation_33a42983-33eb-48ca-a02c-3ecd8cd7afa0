#!/usr/bin/env python3
"""
Production CLI tool for querying historical market data from S3.

This tool provides interactive querying capabilities for historical Morpho market data
stored in S3 Parquet files. Designed for production operations and data analysis.
"""

import argparse
import boto3
import json
import sys
import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.config import config


class S3HistoricalDataQuery:
    """Production tool for querying historical market data from S3."""
    
    def __init__(self, region: str = None, bucket: str = None):
        self.region = region or config.aws_region
        self.bucket = bucket or config.s3_bucket_name
        self.s3_client = boto3.client('s3', region_name=self.region)
        
    def query_data(self,
                   start_time: datetime,
                   end_time: datetime,
                   asset_symbols: List[str] = None,
                   market_keys: List[str] = None,
                   min_apy: float = None,
                   max_apy: float = None,
                   min_borrowed: float = None,
                   output_format: str = 'json') -> Dict:
        """
        Query historical data with comprehensive filtering.
        
        Args:
            start_time: Start of time range
            end_time: End of time range
            asset_symbols: Filter by loan or collateral asset symbols
            market_keys: Filter by specific market unique keys
            min_apy: Minimum supply APY threshold
            max_apy: Maximum supply APY threshold
            min_borrowed: Minimum borrowed amount in USD
            output_format: Output format ('json', 'csv', 'table')
            
        Returns:
            Dictionary with query results and metadata
        """
        try:
            # Get S3 objects in time range
            s3_objects = self._get_s3_objects_in_range(start_time, end_time)
            
            if not s3_objects:
                return {
                    'status': 'success',
                    'message': 'No data found in specified time range',
                    'data': [],
                    'metadata': {
                        'total_records': 0,
                        'time_range': f"{start_time.isoformat()} to {end_time.isoformat()}",
                        'filters_applied': self._get_filters_summary(asset_symbols, market_keys, min_apy, max_apy, min_borrowed)
                    }
                }
            
            # Read and process data
            all_data = []
            processed_files = 0
            
            for obj_key in s3_objects[:100]:  # Limit to 100 files for performance
                try:
                    obj = self.s3_client.get_object(Bucket=self.bucket, Key=obj_key)
                    df = pd.read_parquet(obj['Body'])
                    
                    # Add timestamp from filename
                    file_timestamp = self._extract_timestamp_from_key(obj_key)
                    df['query_timestamp'] = file_timestamp.isoformat()
                    
                    # Apply filters
                    filtered_df = self._apply_filters(df, asset_symbols, market_keys, min_apy, max_apy, min_borrowed)
                    
                    if not filtered_df.empty:
                        all_data.append(filtered_df)
                    
                    processed_files += 1
                    
                except Exception as e:
                    print(f"Warning: Error processing {obj_key}: {e}", file=sys.stderr)
                    continue
            
            if not all_data:
                return {
                    'status': 'success',
                    'message': 'No data matches the specified filters',
                    'data': [],
                    'metadata': {
                        'total_records': 0,
                        'processed_files': processed_files,
                        'time_range': f"{start_time.isoformat()} to {end_time.isoformat()}",
                        'filters_applied': self._get_filters_summary(asset_symbols, market_keys, min_apy, max_apy, min_borrowed)
                    }
                }
            
            # Combine all data
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Sort by timestamp descending
            combined_df = combined_df.sort_values('query_timestamp', ascending=False)
            
            # Convert to output format
            if output_format == 'json':
                data = combined_df.to_dict('records')
            elif output_format == 'csv':
                data = combined_df.to_csv(index=False)
            else:  # table format
                data = combined_df.to_string(index=False)
            
            return {
                'status': 'success',
                'message': f'Successfully retrieved {len(combined_df)} records',
                'data': data,
                'metadata': {
                    'total_records': len(combined_df),
                    'processed_files': processed_files,
                    'unique_markets': combined_df['unique_key'].nunique() if 'unique_key' in combined_df.columns else 0,
                    'time_range': f"{start_time.isoformat()} to {end_time.isoformat()}",
                    'filters_applied': self._get_filters_summary(asset_symbols, market_keys, min_apy, max_apy, min_borrowed)
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Query failed: {str(e)}',
                'data': [],
                'metadata': {}
            }
    
    def _get_s3_objects_in_range(self, start_time: datetime, end_time: datetime) -> List[str]:
        """Get S3 object keys for files in the specified time range."""
        objects = []
        
        # Generate hourly prefixes for the time range
        current = start_time.replace(minute=0, second=0, microsecond=0)
        while current <= end_time:
            prefix = f"markets/year={current.year}/month={current.month:02d}/day={current.day:02d}/hour={current.hour:02d}/"
            
            try:
                response = self.s3_client.list_objects_v2(
                    Bucket=self.bucket,
                    Prefix=prefix
                )
                
                for obj in response.get('Contents', []):
                    file_time = self._extract_timestamp_from_key(obj['Key'])
                    if start_time <= file_time <= end_time:
                        objects.append(obj['Key'])
                        
            except Exception as e:
                # Skip missing prefixes (expected for sparse data)
                continue
            
            current += timedelta(hours=1)
        
        return sorted(objects)
    
    def _extract_timestamp_from_key(self, s3_key: str) -> datetime:
        """Extract timestamp from S3 key."""
        try:
            # Extract from filename: markets_20250910_143000.parquet
            filename = s3_key.split('/')[-1]
            if '_' in filename:
                parts = filename.split('_')
                if len(parts) >= 3:
                    date_str = parts[1]
                    time_str = parts[2].split('.')[0]
                    timestamp_str = f"{date_str}_{time_str}"
                    return datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S').replace(tzinfo=timezone.utc)
        except:
            pass
        
        # Fallback: use current time
        return datetime.now(timezone.utc)
    
    def _apply_filters(self, df: pd.DataFrame, asset_symbols: List[str], market_keys: List[str], 
                      min_apy: float, max_apy: float, min_borrowed: float) -> pd.DataFrame:
        """Apply filtering criteria to DataFrame."""
        filtered_df = df.copy()
        
        # Filter by asset symbols
        if asset_symbols:
            asset_filter = (
                filtered_df['loan_asset_symbol'].isin(asset_symbols) |
                filtered_df['collateral_asset_symbol'].isin(asset_symbols)
            )
            filtered_df = filtered_df[asset_filter]
        
        # Filter by market keys
        if market_keys:
            filtered_df = filtered_df[filtered_df['unique_key'].isin(market_keys)]
        
        # Filter by APY range
        if min_apy is not None:
            filtered_df = filtered_df[filtered_df['supply_apy'] >= min_apy]
        
        if max_apy is not None:
            filtered_df = filtered_df[filtered_df['supply_apy'] <= max_apy]
        
        # Filter by minimum borrowed amount
        if min_borrowed is not None:
            filtered_df = filtered_df[filtered_df['borrow_assets_usd'] >= min_borrowed]
        
        return filtered_df
    
    def _get_filters_summary(self, asset_symbols: List[str], market_keys: List[str],
                           min_apy: float, max_apy: float, min_borrowed: float) -> Dict:
        """Get summary of applied filters."""
        filters = {}
        
        if asset_symbols:
            filters['asset_symbols'] = asset_symbols
        if market_keys:
            filters['market_keys'] = market_keys
        if min_apy is not None:
            filters['min_apy'] = min_apy
        if max_apy is not None:
            filters['max_apy'] = max_apy
        if min_borrowed is not None:
            filters['min_borrowed_usd'] = min_borrowed
        
        return filters


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description='Query historical Morpho market data from S3',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Query last 24 hours of USDC markets
  python query_historical_data.py --hours 24 --assets USDC
  
  # Query specific time range with APY filter
  python query_historical_data.py --start "2025-09-09 00:00:00" --end "2025-09-10 23:59:59" --min-apy 5.0
  
  # Query specific markets with minimum borrowed amount
  python query_historical_data.py --days 7 --market-keys 0x123...abc --min-borrowed 10000
  
  # Export to CSV
  python query_historical_data.py --hours 6 --format csv > markets.csv
        """
    )
    
    # Time range options
    time_group = parser.add_mutually_exclusive_group(required=True)
    time_group.add_argument('--hours', type=int, help='Query last N hours')
    time_group.add_argument('--days', type=int, help='Query last N days')
    time_group.add_argument('--start', type=str, help='Start time (YYYY-MM-DD HH:MM:SS)')
    
    parser.add_argument('--end', type=str, help='End time (YYYY-MM-DD HH:MM:SS) - required with --start')
    
    # Filtering options
    parser.add_argument('--assets', nargs='+', help='Filter by asset symbols (e.g., USDC WETH)')
    parser.add_argument('--market-keys', nargs='+', help='Filter by specific market unique keys')
    parser.add_argument('--min-apy', type=float, help='Minimum supply APY threshold')
    parser.add_argument('--max-apy', type=float, help='Maximum supply APY threshold')
    parser.add_argument('--min-borrowed', type=float, help='Minimum borrowed amount in USD')
    
    # Output options
    parser.add_argument('--format', choices=['json', 'csv', 'table'], default='json',
                       help='Output format (default: json)')
    parser.add_argument('--pretty', action='store_true', help='Pretty print JSON output')
    
    # AWS options
    parser.add_argument('--region', help='AWS region')
    parser.add_argument('--bucket', help='S3 bucket name')
    
    args = parser.parse_args()
    
    # Validate time range arguments
    if args.start and not args.end:
        parser.error("--end is required when using --start")
    
    # Calculate time range
    end_time = datetime.now(timezone.utc)
    
    if args.hours:
        start_time = end_time - timedelta(hours=args.hours)
    elif args.days:
        start_time = end_time - timedelta(days=args.days)
    else:
        try:
            start_time = datetime.fromisoformat(args.start.replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(args.end.replace('Z', '+00:00'))
        except ValueError as e:
            parser.error(f"Invalid datetime format: {e}")
    
    # Initialize query client
    query_client = S3HistoricalDataQuery(region=args.region, bucket=args.bucket)
    
    # Execute query
    result = query_client.query_data(
        start_time=start_time,
        end_time=end_time,
        asset_symbols=args.assets,
        market_keys=args.market_keys,
        min_apy=args.min_apy,
        max_apy=args.max_apy,
        min_borrowed=args.min_borrowed,
        output_format=args.format
    )
    
    # Output results
    if result['status'] == 'error':
        print(f"Error: {result['message']}", file=sys.stderr)
        sys.exit(1)
    
    if args.format == 'json':
        if args.pretty:
            print(json.dumps(result, indent=2))
        else:
            print(json.dumps(result))
    else:
        # For CSV and table formats, just print the data
        print(result['data'])
        
        # Print metadata to stderr so it doesn't interfere with data output
        print(f"# {result['message']}", file=sys.stderr)
        print(f"# Metadata: {json.dumps(result['metadata'])}", file=sys.stderr)


if __name__ == "__main__":
    main()
