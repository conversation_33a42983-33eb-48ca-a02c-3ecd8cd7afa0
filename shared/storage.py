"""
Storage utilities for DynamoDB, S3, and Redis operations.
"""
import json
import logging
import os
import boto3
import redis
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timezone
from decimal import Decimal
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from io import BytesIO

from models.market import Market, Vault
from shared.config import config

logger = logging.getLogger(__name__)


def convert_float_to_decimal(obj: Any) -> Any:
    """Recursively convert float values to Decimal and boolean values to String for DynamoDB compatibility."""
    if isinstance(obj, float):
        if obj != obj:  # Check for NaN
            return None
        return Decimal(str(obj))
    elif isinstance(obj, bool):
        # Convert boolean to string for DynamoDB GSI compatibility
        return "true" if obj else "false"
    elif isinstance(obj, dict):
        return {k: convert_float_to_decimal(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_float_to_decimal(item) for item in obj]
    return obj


def safe_convert_large_numbers(value: Any) -> Union[str, Any]:
    """Safely convert large numbers to string to avoid overflow in pandas."""
    if isinstance(value, (int, float)):
        # Convert very large numbers to string to avoid pandas int64 overflow
        if abs(value) > 9223372036854775807:  # Max int64 value
            return str(value)
    elif isinstance(value, str):
        try:
            # Check if string represents a large number
            num_value = float(value)
            if abs(num_value) > 9223372036854775807:
                return value  # Keep as string
        except (ValueError, OverflowError):
            pass
    return value


class DecimalEncoder(json.JSONEncoder):
    """JSON encoder that handles Decimal objects."""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)


class DynamoDBStorage:
    """DynamoDB storage operations for market data."""
    
    def __init__(self, table_name: str = None, region: str = None):
        self.table_name = table_name or config.dynamodb_table_name
        self.region = region or config.aws_region
        self.dynamodb = boto3.resource('dynamodb', region_name=self.region)
        self.table = self.dynamodb.Table(self.table_name)
    
    def put_market(self, market: Market) -> bool:
        """Store a market in DynamoDB."""
        try:
            item = {
                'PK': f"MARKET#{market.unique_key}",
                'SK': 'LATEST',
                'unique_key': market.unique_key,
                'loan_asset_symbol': market.loan_asset.symbol,
                'collateral_asset_symbol': market.collateral_asset.symbol,
                'loan_asset_id': market.loan_asset.id,
                'collateral_asset_id': market.collateral_asset.id,
                'loan_asset_chain_id': market.loan_asset.chain_id,
                'collateral_asset_chain_id': market.collateral_asset.chain_id,
                'supply_apy': market.state.supply_apy,
                'borrow_assets_usd': market.state.borrow_assets_usd,
                'utilization': market.state.utilization,
                'total_supply_assets': market.state.total_supply_assets,
                'total_borrow_assets': market.state.total_borrow_assets,
                'whitelisted': market.whitelisted,
                'chain_id': market.chain_id,
                'last_updated': market.state.last_updated.isoformat() if market.state.last_updated else None,
                'ttl': int((datetime.now(timezone.utc).timestamp() + 86400 * 7))  # 7 days TTL
            }

            # Remove None values
            item = {k: v for k, v in item.items() if v is not None}

            # Convert float values to Decimal for DynamoDB compatibility
            item = convert_float_to_decimal(item)

            self.table.put_item(Item=item)
            logger.debug(f"Stored market {market.unique_key} in DynamoDB")
            return True

        except Exception as e:
            logger.error(f"Error storing market {market.unique_key} in DynamoDB: {e}")
            return False
    
    def put_markets_batch(self, markets: List[Market]) -> int:
        """Store multiple markets in DynamoDB using batch operations."""
        # Deduplicate markets by unique_key to avoid batch conflicts
        unique_markets = {}
        for market in markets:
            unique_markets[market.unique_key] = market

        deduplicated_markets = list(unique_markets.values())

        if len(deduplicated_markets) != len(markets):
            logger.info(f"Deduplicated {len(markets)} markets to {len(deduplicated_markets)} unique markets")

        success_count = 0

        # Create a single timestamp for this batch operation
        timestamp = datetime.now(timezone.utc)

        # Process in batches of 25 (increased from 12 since we only store 1 record per market now)
        # This ensures we don't exceed DynamoDB's 25 item limit per batch
        for i in range(0, len(deduplicated_markets), 25):
            batch = deduplicated_markets[i:i+25]

            try:
                with self.table.batch_writer() as batch_writer:
                    for market in batch:
                        # Base item data
                        base_item = {
                            'unique_key': market.unique_key,
                            'loan_asset_symbol': market.loan_asset.symbol,
                            'collateral_asset_symbol': market.collateral_asset.symbol,
                            'loan_asset_id': market.loan_asset.id,
                            'collateral_asset_id': market.collateral_asset.id,
                            'loan_asset_chain_id': market.loan_asset.chain_id,
                            'collateral_asset_chain_id': market.collateral_asset.chain_id,
                            'supply_apy': market.state.supply_apy,
                            'borrow_assets_usd': market.state.borrow_assets_usd,
                            'utilization': market.state.utilization,
                            'total_supply_assets': market.state.total_supply_assets,
                            'total_borrow_assets': market.state.total_borrow_assets,
                            'whitelisted': market.whitelisted,
                            'chain_id': market.chain_id,
                            'last_updated': market.state.last_updated.isoformat() if market.state.last_updated else None,
                        }

                        # Remove None values
                        base_item = {k: v for k, v in base_item.items() if v is not None}

                        # Convert float values to Decimal for DynamoDB compatibility
                        base_item = convert_float_to_decimal(base_item)

                        # Create LATEST record (for fast bot queries)
                        latest_item = {
                            'PK': f"MARKET#{market.unique_key}",
                            'SK': 'LATEST',
                            'ttl': int((timestamp.timestamp() + 86400 * 7)),  # 7 days TTL
                            **base_item
                        }

                        # Store only LATEST record (Option A: DynamoDB for Latest + S3 for Historical)
                        batch_writer.put_item(Item=latest_item)
                        success_count += 1

            except Exception as e:
                logger.error(f"Error in batch write for markets {i}-{i+len(batch)}: {e}")

        logger.info(f"Successfully stored {success_count}/{len(deduplicated_markets)} markets in DynamoDB")
        return success_count
    
    def get_markets_by_assets(self,
                             loan_symbol: Optional[str] = None,
                             collateral_symbol: Optional[str] = None) -> List[Market]:
        """Get markets filtered by asset symbols."""
        try:
            # Build filter expression - ONLY get LATEST records to avoid duplicates
            filter_expressions = ["SK = :sk"]
            expression_values = {':sk': 'LATEST'}

            if loan_symbol:
                filter_expressions.append("loan_asset_symbol = :loan_symbol")
                expression_values[':loan_symbol'] = loan_symbol

            if collateral_symbol:
                filter_expressions.append("collateral_asset_symbol = :collateral_symbol")
                expression_values[':collateral_symbol'] = collateral_symbol

            scan_kwargs = {
                'FilterExpression': ' AND '.join(filter_expressions),
                'ExpressionAttributeValues': expression_values
            }

            response = self.table.scan(**scan_kwargs)
            items = response.get('Items', [])

            # Convert to Market objects with deduplication
            markets = []
            seen_keys = set()
            for item in items:
                try:
                    market = self._item_to_market(item)
                    if market and market.unique_key not in seen_keys:
                        markets.append(market)
                        seen_keys.add(market.unique_key)
                    elif market and market.unique_key in seen_keys:
                        logger.warning(f"Duplicate market found and skipped: {market.unique_key}")
                except Exception as e:
                    logger.warning(f"Error converting DynamoDB item to Market: {e}")

            logger.info(f"Retrieved {len(markets)} unique markets from DynamoDB")
            return markets

        except Exception as e:
            logger.error(f"Error retrieving markets from DynamoDB: {e}")
            return []
    
    def _item_to_market(self, item: Dict[str, Any]) -> Optional[Market]:
        """Convert DynamoDB item to Market object."""
        try:
            from models.market import Asset, MarketState
            
            loan_asset = Asset(
                id=item.get('loan_asset_id', ''),
                symbol=item.get('loan_asset_symbol', 'UNKNOWN'),
                chain_id=item.get('loan_asset_chain_id')
            )
            
            collateral_asset = Asset(
                id=item.get('collateral_asset_id', ''),
                symbol=item.get('collateral_asset_symbol', 'UNKNOWN'),
                chain_id=item.get('collateral_asset_chain_id')
            )
            
            last_updated = None
            if item.get('last_updated'):
                last_updated = datetime.fromisoformat(item['last_updated'])
            
            market_state = MarketState(
                supply_apy=item.get('supply_apy'),
                borrow_assets_usd=item.get('borrow_assets_usd'),
                utilization=item.get('utilization'),
                total_supply_assets=item.get('total_supply_assets'),
                total_borrow_assets=item.get('total_borrow_assets'),
                last_updated=last_updated
            )
            
            # Convert whitelisted string back to boolean
            whitelisted_value = item.get('whitelisted', True)
            if isinstance(whitelisted_value, str):
                whitelisted_value = whitelisted_value.lower() == "true"

            return Market(
                unique_key=item['unique_key'],
                loan_asset=loan_asset,
                collateral_asset=collateral_asset,
                state=market_state,
                whitelisted=whitelisted_value,
                chain_id=item.get('chain_id', 1)
            )
            
        except Exception as e:
            logger.error(f"Error converting DynamoDB item to Market: {e}")
            return None

    def get_all_latest_markets(self) -> List[Market]:
        """Get all LATEST market records (fallback method)."""
        try:
            response = self.table.scan(
                FilterExpression='SK = :sk',
                ExpressionAttributeValues={':sk': 'LATEST'}
            )
            items = response.get('Items', [])

            # Convert to Market objects with deduplication
            markets = []
            seen_keys = set()
            for item in items:
                try:
                    market = self._item_to_market(item)
                    if market and market.unique_key not in seen_keys:
                        markets.append(market)
                        seen_keys.add(market.unique_key)
                except Exception as e:
                    logger.warning(f"Error converting DynamoDB item to Market: {e}")

            logger.info(f"Retrieved {len(markets)} markets from DynamoDB (all LATEST)")
            return markets

        except Exception as e:
            logger.error(f"Error retrieving all LATEST markets from DynamoDB: {e}")
            return []




class S3Storage:
    """S3 storage operations for historical data."""
    
    def __init__(self, bucket_name: str = None, region: str = None):
        self.bucket_name = bucket_name or config.s3_bucket_name
        self.region = region or config.aws_region
        self.s3_client = boto3.client('s3', region_name=self.region)
    
    def store_markets_parquet(self, markets: List[Market], timestamp: datetime = None) -> bool:
        """Store markets data as Parquet in S3."""
        if not markets:
            logger.warning("No markets to store in S3")
            return False

        timestamp = timestamp or datetime.now(timezone.utc)

        try:
            # Convert markets to DataFrame
            data = []
            for market in markets:
                row = {
                    'unique_key': market.unique_key,
                    'loan_asset_symbol': market.loan_asset.symbol,
                    'collateral_asset_symbol': market.collateral_asset.symbol,
                    'loan_asset_id': market.loan_asset.id,
                    'collateral_asset_id': market.collateral_asset.id,
                    'loan_asset_chain_id': market.loan_asset.chain_id,
                    'collateral_asset_chain_id': market.collateral_asset.chain_id,
                    'supply_apy': market.state.supply_apy,
                    'borrow_assets_usd': market.state.borrow_assets_usd,
                    'utilization': market.state.utilization,
                    'total_supply_assets': safe_convert_large_numbers(market.state.total_supply_assets),
                    'total_borrow_assets': safe_convert_large_numbers(market.state.total_borrow_assets),
                    'whitelisted': market.whitelisted,
                    'chain_id': market.chain_id,
                    'snapshot_timestamp': timestamp.isoformat()
                }
                data.append(row)

            df = pd.DataFrame(data)

            # Explicitly set data types to handle large numbers
            # Convert large numeric columns to string type to avoid int64 overflow
            for col in ['total_supply_assets', 'total_borrow_assets']:
                if col in df.columns:
                    # Convert to string if any values are strings (indicating large numbers)
                    if df[col].dtype == 'object':
                        df[col] = df[col].astype(str)

            # Create Parquet buffer
            buffer = BytesIO()
            table = pa.Table.from_pandas(df)
            pq.write_table(table, buffer)
            buffer.seek(0)
            
            # Generate S3 key with partitioning
            year = timestamp.strftime('%Y')
            month = timestamp.strftime('%m')
            day = timestamp.strftime('%d')
            hour = timestamp.strftime('%H')
            
            s3_key = f"markets/year={year}/month={month}/day={day}/hour={hour}/markets_{timestamp.strftime('%Y%m%d_%H%M%S')}.parquet"
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=buffer.getvalue(),
                ContentType='application/octet-stream'
            )
            
            logger.info(f"Stored {len(markets)} markets in S3: s3://{self.bucket_name}/{s3_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing markets in S3: {e}")
            return False


class RedisCache:
    """Redis cache operations for fast data access."""

    def __init__(self, cluster_endpoint: str = None, auth_token: str = None):
        self.cluster_endpoint = cluster_endpoint or config.redis_cluster_endpoint
        if not self.cluster_endpoint:
            raise ValueError("Redis cluster endpoint not configured")

        # Parse endpoint
        if '://' in self.cluster_endpoint:
            self.cluster_endpoint = self.cluster_endpoint.split('://')[-1]

        host, port = self.cluster_endpoint.split(':')

        # Get auth token from environment or parameter
        redis_auth = auth_token or os.getenv('REDIS_AUTH_TOKEN')

        self.redis_client = redis.Redis(
            host=host,
            port=int(port),
            password=redis_auth,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            ssl=True if redis_auth else False  # Use SSL if auth is enabled
        )
    
    def cache_markets(self, markets: List[Market], key_prefix: str = "markets") -> bool:
        """Cache markets data in Redis."""
        try:
            pipe = self.redis_client.pipeline()
            
            for market in markets:
                cache_key = f"{key_prefix}:{market.unique_key}"
                market_data = json.dumps(market.to_dict(), cls=DecimalEncoder)
                pipe.setex(cache_key, config.cache_ttl_seconds, market_data)
            
            # Cache market list by asset pairs
            asset_pairs = {}
            for market in markets:
                loan_symbol = market.loan_asset.symbol
                collateral_symbol = market.collateral_asset.symbol
                
                # Group by loan asset
                if loan_symbol not in asset_pairs:
                    asset_pairs[loan_symbol] = []
                asset_pairs[loan_symbol].append(market.unique_key)
                
                # Group by collateral asset
                collateral_key = f"collateral:{collateral_symbol}"
                if collateral_key not in asset_pairs:
                    asset_pairs[collateral_key] = []
                asset_pairs[collateral_key].append(market.unique_key)
            
            # Cache asset pair groupings
            for asset_key, market_keys in asset_pairs.items():
                cache_key = f"{key_prefix}:by_asset:{asset_key}"
                pipe.setex(cache_key, config.cache_ttl_seconds, json.dumps(market_keys))
            
            pipe.execute()
            logger.info(f"Cached {len(markets)} markets in Redis")
            return True
            
        except Exception as e:
            logger.error(f"Error caching markets in Redis: {e}")
            return False
    
    def get_cached_markets(self, 
                          loan_symbol: Optional[str] = None,
                          collateral_symbol: Optional[str] = None) -> List[Market]:
        """Get cached markets from Redis."""
        try:
            market_keys = []
            
            if loan_symbol:
                cache_key = f"markets:by_asset:{loan_symbol}"
                cached_keys = self.redis_client.get(cache_key)
                if cached_keys:
                    market_keys.extend(json.loads(cached_keys))
            
            if collateral_symbol:
                cache_key = f"markets:by_asset:collateral:{collateral_symbol}"
                cached_keys = self.redis_client.get(cache_key)
                if cached_keys:
                    if market_keys:
                        # Intersection of both filters
                        market_keys = list(set(market_keys) & set(json.loads(cached_keys)))
                    else:
                        market_keys.extend(json.loads(cached_keys))
            
            if not market_keys and not loan_symbol and not collateral_symbol:
                # Get all markets
                pattern = "markets:*"
                all_keys = self.redis_client.keys(pattern)
                market_keys = [key.split(':')[-1] for key in all_keys if ':by_asset:' not in key]
            
            # Fetch market data
            markets = []
            if market_keys:
                pipe = self.redis_client.pipeline()
                for key in market_keys:
                    pipe.get(f"markets:{key}")
                
                results = pipe.execute()
                for result in results:
                    if result:
                        try:
                            market_data = json.loads(result)
                            market = Market.from_dict(market_data)
                            markets.append(market)
                        except Exception as e:
                            logger.warning(f"Error parsing cached market data: {e}")
            
            logger.info(f"Retrieved {len(markets)} markets from Redis cache")
            return markets
            
        except Exception as e:
            logger.error(f"Error retrieving markets from Redis: {e}")
            return []
