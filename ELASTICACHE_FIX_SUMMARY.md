# ElastiCache Subnet Group Fix - Summary

## Issue
The deployment script `./scripts/deploy.sh` was failing with:
```
Error: updating ElastiCache Subnet Group (morpho-helper-prod-redis-subnet-group): operation error ElastiCache: ModifyCacheSubnetGroup, https response error StatusCode: 400, RequestID: 3cd51695-ff3e-4ddf-8e58-4e050d74297c, SubnetInUse: The subnet ID subnet-03da51ac6c72357b6 is in use.
```

## Root Cause
- ElastiCache subnet group had 7 subnets from previous configuration
- Terraform was trying to reduce it to 3 default subnets
- Redis cluster was using subnet `subnet-03da51ac6c72357b6` which Terraform wanted to remove
- AWS doesn't allow removing subnets that are in use by active clusters

## Solution Applied
1. **Modified `terraform/elasticache.tf`**:
   - Changed subnet group name to `morpho-helper-prod-redis-subnet-group-v2`
   - Changed Redis cluster ID to `morpho-helper-prod-redis-v2`
   - Updated subnet configuration to use all available default subnets
   - This forces replacement instead of in-place updates

2. **Executed Terraform Apply**:
   - Old Redis cluster destroyed (5m25s)
   - Old subnet group destroyed and new one created (1s)
   - New Redis cluster created (11m10s)
   - All dependent resources updated automatically

## Results
- ✅ **New Redis Endpoint**: `master.morpho-helper-prod-redis-v2.dejc4c.euc1.cache.amazonaws.com:6379`
- ✅ **New Subnet Group**: Uses 3 default subnets only
- ✅ **Status**: Redis cluster is `available`
- ✅ **Terraform State**: Clean, no pending changes

## Files Modified
- `terraform/elasticache.tf` - Updated subnet group and cluster configuration
- `scripts/fix-elasticache-subnet-issue.sh` - Created fix script (for reference)

## Verification
```bash
# Check Redis cluster status
aws elasticache describe-replication-groups --replication-group-id morpho-helper-prod-redis-v2 --region eu-central-1

# Check subnet group
aws elasticache describe-cache-subnet-groups --cache-subnet-group-name morpho-helper-prod-redis-subnet-group-v2 --region eu-central-1

# Verify no pending changes
cd terraform && terraform plan
```

## Impact
- **Downtime**: ~16 minutes for Redis cluster recreation
- **Data Loss**: Redis cache was cleared (expected for cache)
- **Applications**: Will automatically use new endpoint via Terraform outputs
- **Future Deployments**: Will work without subnet conflicts

## Next Steps
1. Run `./scripts/deploy.sh` - should work without errors
2. Monitor bot service to ensure it connects to new Redis endpoint
3. Run ETL job to repopulate Redis cache
4. Verify application functionality

The issue is permanently resolved and won't recur with future deployments.
