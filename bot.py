import os
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import ApplicationBuilder, CommandHandler, ContextTypes
import helper

load_dotenv()
TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Main command handler for the telegram bot.

    Usage:
    /start - Get all new whitelisted markets
    /start [collateral_asset] - Filter by collateral asset only
    /start [collateral_asset] [loan_asset] - Filter by both assets
    /start _ [loan_asset] - Filter by loan asset only (use _ as placeholder for collateral)

    Examples:
    /start WETH
    /start WETH USDC
    /start _ USDC
    """
    args = context.args if hasattr(context, "args") else []

    # Parse arguments with support for placeholder
    collateral_asset = None
    loan_asset = None

    if len(args) > 0 and args[0] != "_":
        collateral_asset = args[0]

    if len(args) > 1 and args[1] != "_":
        loan_asset = args[1]

    result = helper.get_new_whitelisted_markets_for_vault(
        collateral_asset_filter=collateral_asset,
        loan_asset_filter=loan_asset
    )
    await update.message.reply_text(result)

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    Provides help information about bot usage.
    """
    help_text = """
🤖 **Morpho Markets Bot Help**

This bot helps you find new whitelisted markets not yet in your vault.

**Commands:**
• `/start` - Get all new whitelisted markets
• `/start [collateral_asset]` - Filter by collateral asset only
• `/start [collateral_asset] [loan_asset]` - Filter by both assets
• `/start _ [loan_asset]` - Filter by loan asset only (use _ as placeholder)
• `/help` - Show this help message

**Examples:**
• `/start` - All new markets
• `/start WETH` - Markets with WETH as collateral asset
• `/start WETH USDC` - Markets with WETH collateral and USDC loan
• `/start _ USDC` - Markets with USDC as loan asset

**Market Information Displayed:**
• Asset pair (collateral/loan)
• Supply APY
• Total borrowed amount (USD)
• Utilization rate
• Unique market key
    """
    await update.message.reply_text(help_text, parse_mode='Markdown')

if __name__ == "__main__":
    if not TELEGRAM_TOKEN:
        print("Please set TELEGRAM_TOKEN in your .env file.")
    else:
        app = ApplicationBuilder().token(TELEGRAM_TOKEN).build()
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("help", help_command))
        print("Bot is running...")
        print("Available commands: /start, /help")
        app.run_polling()
