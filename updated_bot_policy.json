{"Version": "2012-10-17", "Statement": [{"Action": ["dynamodb:GetItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:DescribeTable"], "Effect": "Allow", "Resource": ["arn:aws:dynamodb:eu-central-1:236840575805:table/morpho-helper-prod-markets", "arn:aws:dynamodb:eu-central-1:236840575805:table/morpho-helper-prod-markets/index/*", "arn:aws:dynamodb:eu-central-1:236840575805:table/morpho-helper-prod-vaults", "arn:aws:dynamodb:eu-central-1:236840575805:table/morpho-helper-prod-vaults/index/*"]}, {"Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": "arn:aws:logs:eu-central-1:236840575805:log-group:/ecs/morpho-helper-prod-bot:*"}]}