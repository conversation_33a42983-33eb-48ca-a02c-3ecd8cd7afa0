# Morpho Helper - Production-Ready Telegram Bot

A scalable, cloud-native Telegram bot that monitors Morpho lending markets and provides real-time insights about new whitelisted markets not yet in your vault. Built with AWS best practices for production workloads.

## 📋 Table of Contents

- [Architecture Overview](#-architecture-overview)
- [Features](#-features)
- [Quick Start](#-quick-start)
- [Deployment Guide](#-deployment-guide)
- [Configuration](#-configuration)
- [Data Schema](#-data-schema)
- [Bot Commands](#-bot-commands)
- [Monitoring & Observability](#-monitoring--observability)
- [Cost Optimization](#-cost-optimization)
- [Security](#-security)
- [Troubleshooting](#-troubleshooting)
- [Development](#-development)
- [Contributing](#-contributing)

## 🏗️ Architecture Overview

This application implements a cloud-native architecture with clear separation of concerns:

### Core Components
- **Bot Service**: ECS Fargate service for always-on Telegram bot
- **ETL Pipeline**: AWS Batch job for scheduled data collection with pagination
- **Multi-tiered Storage**: S3 (data lake) → DynamoDB (latest state) → Redis (cache)
- **Infrastructure as Code**: Complete Terraform configuration
- **Monitoring**: CloudWatch dashboards, alarms, and SNS notifications

### Architecture Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Telegram      │    │   AWS Batch      │    │   Morpho API    │
│   Users         │    │   ETL Job        │    │   (Paginated)   │
│                 │    │   (Hourly)       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       │
┌─────────────────┐    ┌──────────────────┐              │
│   ECS Fargate   │    │   Amazon S3      │              │
│   Bot Service   │    │   Data Lake      │              │
│   (Auto-Scale)  │    │   (Parquet)      │              │
└─────────────────┘    └──────────────────┘              │
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       │
┌─────────────────┐    ┌──────────────────┐              │
│  ElastiCache    │    │   DynamoDB       │              │
│  Redis          │◄───│   All Markets    │◄─────────────┘
│  (New Markets)  │    │   (Latest State) │
└─────────────────┘    └──────────────────┘
```

### Data Flow
1. **ETL Pipeline**: Fetches ALL whitelisted markets from Morpho API with pagination
2. **Storage**: Stores complete dataset in S3 (historical) and DynamoDB (latest)
3. **Cache**: Redis caches only new markets (not in vault) for bot performance
4. **Bot Service**: Reads from Redis first, falls back to DynamoDB, never calls API directly

## 🚀 Features

### Core Functionality
- **Real-time Market Monitoring**: Hourly updates with complete market data
- **Fast Response Times**: Sub-second responses via multi-tiered caching
- **Intelligent Filtering**: Shows only markets not yet in your vault
- **Comprehensive Data**: All whitelisted markets stored for analysis

### Production Features
- **Auto-scaling**: ECS Fargate scales 1-5 instances based on load
- **High Availability**: Multi-AZ deployment with health checks
- **Cost Optimization**: Spot instances, VPC endpoints, lifecycle policies
- **Security**: Least privilege IAM, encryption, private subnets
- **Monitoring**: Real-time dashboards, alerts, and logging
- **Retry Logic**: Exponential backoff for API calls and error handling

### Advanced Capabilities
- **Historical Data Lake**: Complete market history in S3 Parquet format
- **Pagination Support**: Fetches all markets regardless of API limits
- **Multi-Vault Ready**: Architecture supports monitoring multiple vaults
- **ML Ready**: Data lake prepared for machine learning integration

## 🚀 Quick Start

### Prerequisites
- AWS CLI configured with appropriate permissions
- Terraform >= 1.0
- Docker and Docker Compose
- Telegram Bot Token (from @BotFather)

### Local Development

1. **Clone and setup**:
   ```bash
   git clone <repository>
   cd morpho_helper
   ./scripts/local-dev.sh setup
   ```

2. **Add your Telegram token** to `.env` file:
   ```bash
   TELEGRAM_TOKEN=your_bot_token_here
   ```

3. **Start the bot**:
   ```bash
   ./scripts/local-dev.sh bot
   ```

### AWS Production Deployment

1. **Set environment variables**:
   ```bash
   export TELEGRAM_TOKEN="your_bot_token_here"
   export AWS_REGION="us-east-1"
   ```

2. **Deploy infrastructure**:
   ```bash
   ./scripts/deploy.sh prod
   ```

3. **The deployment includes**:
   - Complete AWS infrastructure via Terraform
   - Docker images built and pushed to ECR
   - Secrets configured in AWS Secrets Manager
   - Bot service deployed to ECS Fargate
   - ETL job scheduled in AWS Batch
   - SNS alerts configured for team emails
   - CloudWatch monitoring and dashboards

## 🚀 Deployment Guide

### Environment Configuration

Create `terraform/terraform.tfvars`:

```hcl
# Basic Configuration
environment = "prod"
aws_region = "us-east-1"
project_name = "morpho-helper"

# Morpho Configuration
vault_address = "0x62fE596d59fB077c2Df736dF212E0AFfb522dC78"
minimal_borrowed_amount = 100000

# Resource Sizing
bot_cpu = 512      # 0.5 vCPU
bot_memory = 1024  # 1 GB
etl_cpu = 1024     # 1 vCPU
etl_memory = 2048  # 2 GB

# High Availability
bot_desired_count = 2
availability_zone_count = 2

# Cost Optimization
enable_spot_instances = true
single_nat_gateway = false  # true for cost savings, false for HA
enable_vpc_endpoints = true

# Security
enable_deletion_protection = true  # for production
```

### Step-by-Step Deployment

1. **Prepare Environment**:
   ```bash
   # Set required environment variables
   export TELEGRAM_TOKEN="your_bot_token_here"
   export AWS_REGION="us-east-1"

   # Verify AWS credentials
   aws sts get-caller-identity
   ```

2. **Deploy Infrastructure**:
   ```bash
   # Deploy to production
   ./scripts/deploy.sh prod

   # Or deploy to development
   ./scripts/deploy.sh dev
   ```

3. **Verify Deployment**:
   ```bash
   # Check ECS service status
   aws ecs describe-services \
     --cluster morpho-helper-prod-cluster \
     --services morpho-helper-prod-bot

   # Check ETL job queue
   aws batch describe-job-queues \
     --job-queues morpho-helper-prod-etl-job-queue

   # Test bot functionality
   # Send /start to your bot on Telegram
   ```

### Post-Deployment Configuration

1. **Confirm SNS Subscriptions**:
   - Check email for SNS subscription confirmations
   - Confirm subscriptions for: <EMAIL>, <EMAIL>, <EMAIL>

2. **Monitor Initial ETL Run**:
   ```bash
   # Check ETL job logs
   aws logs tail /aws/batch/job --follow
   ```

3. **Verify Data Flow**:
   ```bash
   # Check DynamoDB table
   aws dynamodb scan --table-name morpho-helper-prod-markets --limit 5

   # Check S3 bucket
   aws s3 ls s3://morpho-helper-prod-data-lake-xxxxx/markets/
   ```

## 🎯 Bot Commands

- `/start` - Get all new whitelisted markets not in vault
- `/start USDC` - Filter by loan asset (USDC)
- `/start USDC WETH` - Filter by loan and collateral assets
- `/start _ WETH` - Filter by collateral asset only (use _ as placeholder)
- `/help` - Show detailed help message
- `/status` - Show bot status, data freshness, and connectivity

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `TELEGRAM_TOKEN` | Telegram bot token from @BotFather | - | ✅ |
| `VAULT_ADDRESS` | Morpho vault address to monitor | `0x62fE...` | ✅ |
| `MINIMAL_BORROWED_AMOUNT` | Minimum borrowed threshold | `100000` | ❌ |
| `AWS_REGION` | AWS region for deployment | `us-east-1` | ❌ |
| `REDIS_AUTH_TOKEN` | Redis authentication token | - | ✅ (AWS) |

### Terraform Variables

Key configuration options in `terraform/terraform.tfvars`:

```hcl
# Environment
environment = "prod"  # or "dev", "staging"
aws_region = "us-east-1"

# Resource Sizing
bot_cpu = 512         # CPU units (1024 = 1 vCPU)
bot_memory = 1024     # Memory in MB
etl_cpu = 1024        # ETL job CPU
etl_memory = 2048     # ETL job memory

# Scaling
bot_desired_count = 2           # Number of bot instances
availability_zone_count = 2     # AZs to use (2 for cost, 3+ for HA)

# Cost Optimization
enable_spot_instances = true    # Use Spot for ETL (50% savings)
single_nat_gateway = false     # Single NAT for cost savings
enable_vpc_endpoints = true    # Reduce NAT costs

# Security
enable_deletion_protection = true  # Protect critical resources
```

## 📊 Data Schema

### DynamoDB Schema

**Table**: `morpho-markets`

**Primary Key Design**:
- **Partition Key (PK)**: `MARKET#{unique_key}`
- **Sort Key (SK)**: `LATEST`

**Attributes**:
| Attribute | Type | Description | Example |
|-----------|------|-------------|---------|
| `unique_key` | String | Market identifier | `0x1234...` |
| `loan_asset_symbol` | String | Loan asset | `USDC` |
| `collateral_asset_symbol` | String | Collateral asset | `WETH` |
| `supply_apy` | Number | Supply APY (decimal) | `0.0523` |
| `borrow_assets_usd` | Number | Borrowed amount USD | `1250000.50` |
| `utilization` | Number | Utilization rate | `0.75` |
| `whitelisted` | Boolean | Whitelisted status | `true` |
| `last_updated` | String | ISO timestamp | `2024-01-15T10:30:00Z` |

**Global Secondary Indexes**:
1. **AssetFilterIndex**: `loan_asset_symbol` + `collateral_asset_symbol`
2. **APYRankingIndex**: `whitelisted` + `supply_apy`

### S3 Data Lake Schema

**Bucket Structure**: `morpho-data-lake`

```
s3://morpho-data-lake/
├── markets/
│   ├── year=2024/
│   │   ├── month=01/
│   │   │   ├── day=15/
│   │   │   │   ├── hour=10/
│   │   │   │   │   └── markets_20240115_103000.parquet
│   │   │   │   └── hour=11/
│   │   │   └── day=16/
│   │   └── month=02/
│   └── year=2025/
└── raw/
    └── api_responses/
        └── year=2024/
            └── month=01/
                └── day=15/
                    └── morpho_api_20240115_103000.json
```

**Parquet Schema**:
- Columnar format for efficient querying
- GZIP compression for storage optimization
- Partitioned by time for cost-effective analytics

### Redis Cache Schema

**Key Patterns**:
- `markets:{unique_key}` - Individual market data
- `markets:by_asset:{symbol}` - Markets by loan asset
- `markets:by_asset:collateral:{symbol}` - Markets by collateral
- `vault:{address}:allocations` - Vault market allocations

**TTL**: 300 seconds (5 minutes) for real-time updates

## 📁 Project Structure

```
morpho_helper/
├── bot/                    # Telegram bot service
│   ├── handlers.py         # Command handlers
│   ├── services.py         # Business logic
│   ├── main.py            # Entry point with health checks
│   └── Dockerfile         # Multi-stage bot container
├── etl/                   # ETL job with pagination
│   ├── job.py             # ETL pipeline with retry logic
│   └── Dockerfile         # ETL container
├── models/                # Data models
│   └── market.py          # Market, Asset, Vault models
├── shared/                # Shared utilities
│   ├── config.py          # Configuration with secrets support
│   ├── morpho_client.py   # API client with pagination & retry
│   └── storage.py         # DynamoDB, S3, Redis utilities
├── terraform/             # Infrastructure as Code
│   ├── main.tf            # Main configuration
│   ├── vpc.tf             # VPC with cost optimization
│   ├── ecs.tf             # ECS Fargate with auto-scaling
│   ├── batch.tf           # AWS Batch with Spot instances
│   ├── dynamodb.tf        # DynamoDB with GSIs
│   ├── elasticache.tf     # Redis with auth
│   ├── s3.tf              # S3 with lifecycle policies
│   ├── iam.tf             # Least privilege IAM roles
│   ├── monitoring.tf      # CloudWatch dashboards & alarms
│   └── secrets.tf         # Secrets Manager integration
├── scripts/               # Deployment automation
│   ├── deploy.sh          # Fully automated AWS deployment
│   └── local-dev.sh       # Local development environment
└── README.md              # This comprehensive guide
```

## 📊 Monitoring & Observability

### CloudWatch Dashboard

Access comprehensive monitoring at: AWS Console → CloudWatch → Dashboards → `morpho-helper-{env}-dashboard`

**Key Metrics**:
- **Bot Service**: CPU, memory, task count, response times
- **Redis**: CPU, memory usage, connections, hit rate
- **DynamoDB**: Read/write capacity, throttling, latency
- **ETL Jobs**: Success rate, duration, error count
- **Costs**: Daily spend breakdown by service

### Alerting

**Automated Alerts** (via SNS to team emails):
- Bot service down or high CPU/memory
- Redis high memory usage or connection issues
- DynamoDB throttling or high latency
- ETL job failures or timeouts
- Unusual cost spikes

**Alert Recipients**:
- <EMAIL>
- <EMAIL>
- <EMAIL>

### Logging

**Centralized Logging**:
- **Bot Service**: `/ecs/morpho-helper-{env}-bot`
- **ETL Jobs**: `/aws/batch/job`
- **Redis**: Slow query logs

**Log Retention**:
- Bot logs: 14 days
- ETL logs: 14 days
- Redis logs: 7 days

### Health Checks

- **Bot Service**: HTTP endpoint at `/health`
- **ETL Jobs**: Success/failure tracking in CloudWatch
- **Redis**: Connection monitoring
- **DynamoDB**: Automatic health monitoring

## 💰 Cost Optimization

### Current Optimizations

**Infrastructure Costs** (~$50-100/month for production):

| Service | Monthly Cost | Optimization |
|---------|--------------|--------------|
| ECS Fargate | $15-30 | Right-sized instances, auto-scaling |
| AWS Batch | $5-15 | Spot instances (50% savings) |
| DynamoDB | $5-20 | On-demand billing, TTL cleanup |
| ElastiCache | $15-25 | Right-sized Redis instances |
| S3 | $0.05 | Lifecycle policies, compression |
| NAT Gateway | $10-20 | VPC endpoints, optional single NAT |
| Other | $5-10 | VPC endpoints, monitoring |

### Cost Optimization Features

1. **Spot Instances**: ETL jobs use Spot instances for 50% cost reduction
2. **VPC Endpoints**: Reduce NAT Gateway data transfer costs
3. **S3 Lifecycle**: Automatic transition to cheaper storage classes
4. **Auto Scaling**: Scale down during low usage periods
5. **Right Sizing**: Optimized instance sizes based on actual usage
6. **TTL Cleanup**: Automatic DynamoDB data expiration

### Cost Monitoring

```bash
# Monitor daily costs
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity DAILY \
  --metrics BlendedCost \
  --group-by Type=DIMENSION,Key=SERVICE

# Set up billing alerts
aws budgets create-budget \
  --account-id YOUR_ACCOUNT_ID \
  --budget file://budget.json
```

### Additional Cost Savings

For development environments:
```hcl
# terraform/terraform.tfvars for dev
single_nat_gateway = true          # Single NAT Gateway
availability_zone_count = 2        # Minimum AZs
bot_desired_count = 1             # Single bot instance
enable_deletion_protection = false # Allow easy cleanup
```

## 🔒 Security

### Network Security

- **Private Subnets**: All services run in private subnets
- **Security Groups**: Restrictive rules, least privilege access
- **VPC Endpoints**: Private connectivity to AWS services
- **NAT Gateways**: Controlled internet access for updates

### Access Control

- **IAM Roles**: Least privilege permissions for each service
- **Secrets Manager**: Secure storage for sensitive data
- **No Hardcoded Credentials**: All secrets managed externally
- **Service-Specific Roles**: Separate roles for bot and ETL

### Data Security

- **Encryption at Rest**: All data encrypted (S3, DynamoDB, Redis)
- **Encryption in Transit**: TLS for all communications
- **Redis AUTH**: Authentication required for cache access
- **VPC Isolation**: Network-level isolation

### Compliance

- **Audit Logging**: CloudTrail for all API calls
- **Data Retention**: Configurable retention policies
- **Access Monitoring**: CloudWatch for access patterns
- **Security Scanning**: ECR image scanning enabled

### Security Best Practices

```bash
# Rotate secrets regularly
aws secretsmanager rotate-secret \
  --secret-id morpho-helper-prod/telegram-token

# Review IAM permissions
aws iam simulate-principal-policy \
  --policy-source-arn arn:aws:iam::ACCOUNT:role/morpho-helper-prod-bot-task-role \
  --action-names dynamodb:GetItem

# Monitor security events
aws logs filter-log-events \
  --log-group-name /aws/cloudtrail \
  --filter-pattern "{ $.errorCode = \"*UnauthorizedOperation\" }"
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Bot Not Responding

**Symptoms**: Bot doesn't respond to commands

**Diagnosis**:
```bash
# Check ECS service status
aws ecs describe-services \
  --cluster morpho-helper-prod-cluster \
  --services morpho-helper-prod-bot

# Check task health
aws ecs list-tasks \
  --cluster morpho-helper-prod-cluster \
  --service-name morpho-helper-prod-bot

# Check logs
aws logs tail /ecs/morpho-helper-prod-bot --follow
```

**Solutions**:
- Verify Telegram token in Secrets Manager
- Check security group rules
- Verify ECS task definition
- Check Redis connectivity

#### 2. ETL Job Failing

**Symptoms**: No new data, stale cache

**Diagnosis**:
```bash
# Check Batch job status
aws batch list-jobs \
  --job-queue morpho-helper-prod-etl-job-queue \
  --job-status FAILED

# Check job logs
aws logs tail /aws/batch/job --follow

# Check Morpho API connectivity
curl -X POST https://api.morpho.org/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ markets(first: 1) { items { uniqueKey } } }"}'
```

**Solutions**:
- Check IAM permissions for S3/DynamoDB
- Verify Morpho API availability
- Check network connectivity
- Review retry logic in logs

#### 3. High Costs

**Symptoms**: Unexpected AWS charges

**Diagnosis**:
```bash
# Check cost breakdown
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity DAILY \
  --metrics BlendedCost \
  --group-by Type=DIMENSION,Key=SERVICE

# Check NAT Gateway usage
aws ec2 describe-nat-gateways \
  --filter Name=state,Values=available
```

**Solutions**:
- Enable single NAT Gateway for dev environments
- Use Spot instances for ETL jobs
- Enable VPC endpoints
- Review auto-scaling settings

#### 4. Cache Misses

**Symptoms**: Slow bot responses

**Diagnosis**:
```bash
# Check Redis connectivity
redis-cli -h your-redis-endpoint -p 6379 -a your-auth-token ping

# Check cache hit rate
aws cloudwatch get-metric-statistics \
  --namespace AWS/ElastiCache \
  --metric-name CacheHitRate \
  --dimensions Name=CacheClusterId,Value=morpho-helper-prod-redis \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z \
  --period 3600 \
  --statistics Average
```

**Solutions**:
- Verify ETL job success
- Check Redis memory usage
- Verify cache TTL settings
- Check network connectivity

### Useful Commands

```bash
# Restart bot service
aws ecs update-service \
  --cluster morpho-helper-prod-cluster \
  --service morpho-helper-prod-bot \
  --force-new-deployment

# Trigger ETL job manually
aws batch submit-job \
  --job-name manual-etl-$(date +%s) \
  --job-queue morpho-helper-prod-etl-job-queue \
  --job-definition morpho-helper-prod-etl-job

# Check DynamoDB data
aws dynamodb scan \
  --table-name morpho-helper-prod-markets \
  --limit 5

# Check S3 data
aws s3 ls s3://morpho-helper-prod-data-lake-xxxxx/markets/ --recursive

# Monitor real-time logs
aws logs tail /ecs/morpho-helper-prod-bot --follow
```

### Performance Tuning

#### Bot Service Optimization
```hcl
# Increase resources for high load
bot_cpu = 1024     # 1 vCPU
bot_memory = 2048  # 2 GB
bot_desired_count = 3  # More instances
```

#### Redis Optimization
```hcl
# Upgrade Redis for better performance
redis_node_type = "cache.r6g.large"
redis_num_cache_nodes = 2  # Enable clustering
```

#### ETL Optimization
```hcl
# Faster ETL processing
etl_cpu = 2048     # 2 vCPUs
etl_memory = 4096  # 4 GB
```

## 🛠️ Development

### Local Development Setup

1. **Prerequisites**:
   ```bash
   # Install required tools
   brew install docker docker-compose aws-cli terraform

   # Configure AWS CLI
   aws configure
   ```

2. **Environment Setup**:
   ```bash
   # Clone repository
   git clone <repository>
   cd morpho_helper

   # Set up local environment
   ./scripts/local-dev.sh setup
   ```

3. **Development Workflow**:
   ```bash
   # Start local services
   ./scripts/local-dev.sh start

   # Run ETL job once
   ./scripts/local-dev.sh etl

   # Start bot in development mode
   ./scripts/local-dev.sh bot

   # View logs
   ./scripts/local-dev.sh logs

   # Clean up
   ./scripts/local-dev.sh cleanup
   ```

### Testing

#### Unit Tests
```bash
# Run unit tests
python -m pytest tests/ -v

# Run with coverage
python -m pytest tests/ --cov=shared --cov=bot --cov=etl
```

#### Integration Tests
```bash
# Test Morpho API connectivity
python -c "from shared.morpho_client import MorphoAPIClient; client = MorphoAPIClient(); print(len(client.get_whitelisted_markets()))"

# Test DynamoDB connectivity
python -c "from shared.storage import DynamoDBStorage; storage = DynamoDBStorage(); print('DynamoDB connected')"

# Test Redis connectivity
python -c "from shared.storage import RedisCache; cache = RedisCache(); cache.redis_client.ping(); print('Redis connected')"
```

#### Load Testing
```bash
# Test bot with multiple concurrent requests
for i in {1..10}; do
  curl -X POST "https://api.telegram.org/bot$TELEGRAM_TOKEN/sendMessage" \
    -d "chat_id=$CHAT_ID&text=/start" &
done
```

### Code Quality

#### Linting and Formatting
```bash
# Format code
black shared/ bot/ etl/ models/

# Lint code
flake8 shared/ bot/ etl/ models/

# Type checking
mypy shared/ bot/ etl/ models/
```

#### Security Scanning
```bash
# Scan for security issues
bandit -r shared/ bot/ etl/ models/

# Check dependencies for vulnerabilities
safety check
```

### Contributing Guidelines

1. **Code Style**: Follow PEP 8, use Black for formatting
2. **Testing**: Add tests for new features
3. **Documentation**: Update README for significant changes
4. **Security**: No hardcoded secrets, follow least privilege
5. **Performance**: Consider cost and performance impact

### Development Environment Variables

```bash
# .env for local development
TELEGRAM_TOKEN=your_dev_bot_token
VAULT_ADDRESS=0x62fE596d59fB077c2Df736dF212E0AFfb522dC78
MINIMAL_BORROWED_AMOUNT=100000
AWS_REGION=us-east-1
DYNAMODB_TABLE_NAME=morpho-dev-markets
S3_BUCKET_NAME=morpho-dev-data-lake
REDIS_CLUSTER_ENDPOINT=localhost:6379
REDIS_AUTH_TOKEN=devpassword
```

## 🚀 Scaling for Production

### High Availability Configuration

```hcl
# terraform/terraform.tfvars for HA
availability_zone_count = 3    # Multi-AZ deployment
bot_desired_count = 3         # Multiple bot instances
single_nat_gateway = false    # NAT Gateway per AZ
enable_deletion_protection = true
```

### Performance Scaling

```hcl
# High-performance configuration
bot_cpu = 1024               # 1 vCPU per bot instance
bot_memory = 2048           # 2 GB per bot instance
redis_node_type = "cache.r6g.large"  # Larger Redis instance
etl_cpu = 2048              # 2 vCPUs for ETL
etl_memory = 4096           # 4 GB for ETL
max_vcpus = 100             # Higher ETL concurrency
```

### Enterprise Features

- **Multi-Vault Support**: Monitor multiple vaults simultaneously
- **Advanced Analytics**: Historical trend analysis and predictions
- **API Gateway**: REST API for external integrations
- **Real-time Updates**: WebSocket connections for live data
- **Custom Dashboards**: Grafana integration for advanced visualization

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
name: Deploy Morpho Helper
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Deploy to AWS
        env:
          TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
        run: |
          ./scripts/deploy.sh prod
```

### GitLab CI Example

```yaml
deploy:
  stage: deploy
  image: hashicorp/terraform:latest
  before_script:
    - apk add --no-cache aws-cli docker
  script:
    - ./scripts/deploy.sh prod
  only:
    - main
```

## 📈 Future Enhancements

### Planned Features

1. **Machine Learning Integration**:
   - Market trend prediction using historical data
   - Anomaly detection for unusual market behavior
   - Automated market scoring and recommendations

2. **Advanced Analytics**:
   - Custom dashboards with Grafana
   - Market correlation analysis
   - Performance benchmarking against DeFi indices

3. **Multi-Protocol Support**:
   - Aave market monitoring
   - Compound integration
   - Cross-protocol yield comparison

4. **Enhanced Notifications**:
   - Slack integration
   - Discord bot
   - Email reports with market summaries

5. **API Gateway**:
   - REST API for external access
   - Rate limiting and authentication
   - Webhook support for real-time updates

### Architecture Evolution

```
Current: Morpho API → ETL → Storage → Bot
Future:  Multi-Protocol APIs → ML Pipeline → Advanced Analytics → Multi-Channel Notifications
```

## 🤝 Contributing

### Development Workflow

1. **Fork and Clone**:
   ```bash
   git clone https://github.com/your-username/morpho_helper.git
   cd morpho_helper
   ```

2. **Create Feature Branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Local Development**:
   ```bash
   ./scripts/local-dev.sh setup
   # Make your changes
   ./scripts/local-dev.sh test
   ```

4. **Test Changes**:
   ```bash
   # Run unit tests
   python -m pytest tests/ -v

   # Test deployment
   ./scripts/deploy.sh dev
   ```

5. **Submit Pull Request**:
   - Ensure all tests pass
   - Update documentation if needed
   - Follow code style guidelines

### Code Standards

- **Python**: Follow PEP 8, use Black formatter
- **Terraform**: Use consistent naming and tagging
- **Documentation**: Update README for significant changes
- **Security**: No hardcoded secrets, follow least privilege
- **Testing**: Add tests for new functionality

### Review Process

1. Automated checks (linting, security, tests)
2. Code review by maintainers
3. Deployment testing in dev environment
4. Approval and merge to main
5. Automatic deployment to production

## 📄 License

This project is licensed under the MIT License:

```
MIT License

Copyright (c) 2024 Morpho Helper Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🆘 Support & Maintenance

### Getting Help

1. **Documentation**: Check this README first
2. **Troubleshooting**: Review the troubleshooting section
3. **Logs**: Check CloudWatch logs for detailed error messages
4. **Monitoring**: Use CloudWatch dashboards for system health
5. **Issues**: Create GitHub issues for bugs or feature requests

### Maintenance Schedule

- **Daily**: Automated monitoring and alerting
- **Weekly**: Review costs and performance metrics
- **Monthly**: Security updates and dependency upgrades
- **Quarterly**: Architecture review and optimization

### Support Contacts

- **Technical Issues**: Create GitHub issue
- **Security Concerns**: Email <EMAIL>
- **General Questions**: Email <EMAIL>

### Emergency Procedures

1. **Service Down**: Check CloudWatch alarms and ECS service status
2. **High Costs**: Review cost dashboard and scale down if needed
3. **Security Incident**: Rotate secrets and review access logs
4. **Data Issues**: Check ETL job logs and S3 backup data

---

**Built with ❤️ by the ClearStar team for the DeFi community**

*Last updated: January 2024*
