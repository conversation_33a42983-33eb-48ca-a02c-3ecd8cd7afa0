"""
ETL job for collecting Morpho market data and storing in S3/DynamoDB/Redis.
"""
import os
import logging
import asyncio
import datetime

from shared.config import config
from shared.morpho_client import MorphoAPIClient
from shared.storage import DynamoDBStorage, S3Storage, RedisCache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MorphoETLJob:
    """ETL job for Morpho market data collection."""
    
    def __init__(self):
        # Set service type for configuration validation
        os.environ["SERVICE_TYPE"] = "etl"
        
        self.morpho_client = MorphoAPIClient()
        self.dynamodb_storage = DynamoDBStorage()
        self.s3_storage = S3Storage()
        self.redis_cache = RedisCache()
        
        logger.info("ETL job initialized")
        logger.info(f"Vault address: {config.vault_address}")
        logger.info(f"S3 bucket: {config.s3_bucket_name}")
        logger.info(f"DynamoDB table: {config.dynamodb_table_name}")
    
    async def run(self):
        """Execute the complete ETL pipeline."""
        start_time = datetime.datetime.now(datetime.timezone.utc)
        logger.info(f"Starting ETL job at {start_time}")
        
        try:
            # Step 1: Fetch all whitelisted markets from Morpho API
            logger.info("Step 1: Fetching whitelisted markets from Morpho API")
            all_markets = self.morpho_client.get_whitelisted_markets()
            
            if not all_markets:
                logger.error("No markets fetched from Morpho API")
                return False
            
            logger.info(f"Fetched {len(all_markets)} markets from Morpho API")
            
            # Step 2: Store raw data in S3 (all markets for historical analysis)
            logger.info("Step 2: Storing raw data in S3")
            s3_success = self.s3_storage.store_markets_parquet(all_markets, start_time)

            if not s3_success:
                logger.error("Failed to store data in S3")
                # Continue with other steps even if S3 fails

            # Step 3: Store latest state in DynamoDB (all markets)
            logger.info("Step 3: Storing latest state in DynamoDB")
            dynamodb_count = self.dynamodb_storage.put_markets_batch(all_markets)

            if dynamodb_count == 0:
                logger.error("Failed to store any markets in DynamoDB")
                return False

            # Step 4: Get vault allocations for cache filtering
            logger.info("Step 4: Fetching vault allocations")
            vault = self.morpho_client.get_vault_markets(config.vault_address)
            vault_market_keys = vault.get_market_keys() if vault else set()

            logger.info(f"Found {len(vault_market_keys)} markets in vault across chains {vault.chain_ids if vault else []}")

            # Step 5: Filter new markets (not in vault) for cache
            new_markets = [
                market for market in all_markets
                if market.unique_key not in vault_market_keys
            ]

            logger.info(f"Found {len(new_markets)} new markets not in vault")

            # Step 6: Update Redis cache (only new markets for bot performance)
            logger.info("Step 5: Updating Redis cache")
            redis_success = self.redis_cache.cache_markets(new_markets)

            if not redis_success:
                logger.error("Failed to update Redis cache")
                # Continue as this is not critical

            # Step 7: Store vault allocations for reference
            if vault:
                logger.info("Step 6: Storing vault allocations")
                # Store vault info in DynamoDB for reference
                try:
                    vault_item = {
                        'PK': f"VAULT#{vault.address}",
                        'SK': 'LATEST',
                        'vault_address': vault.address,
                        'chain_id': vault.chain_id,
                        'chain_ids': vault.chain_ids,
                        'market_count': len(vault.allocations),
                        'market_keys': [alloc.market_unique_key for alloc in vault.allocations],
                        'last_updated': start_time.isoformat(),
                        'ttl': int((start_time.timestamp() + 86400 * 7))  # 7 days TTL
                    }

                    # Store in vaults table if it exists, otherwise skip
                    try:
                        self.dynamodb_storage.dynamodb.Table(f"{config.dynamodb_table_name.replace('markets', 'vaults')}").put_item(Item=vault_item)
                        logger.info("Stored vault allocations in DynamoDB")
                    except Exception as e:
                        logger.warning(f"Could not store vault allocations (table may not exist): {e}")

                except Exception as e:
                    logger.warning(f"Error storing vault allocations: {e}")
            
            if not redis_success:
                logger.error("Failed to update Redis cache")
                # Continue as this is not critical
            
            # Step 7: Log summary
            end_time = datetime.datetime.now(datetime.timezone.utc)
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"ETL job completed successfully in {duration:.2f} seconds")
            logger.info(f"Summary:")
            logger.info(f"  - Total markets fetched: {len(all_markets)}")
            logger.info(f"  - All markets stored in DynamoDB: {dynamodb_count}")
            logger.info(f"  - New markets (not in vault): {len(new_markets)}")
            logger.info(f"  - S3 storage: {'Success' if s3_success else 'Failed'}")
            logger.info(f"  - Redis cache: {'Success' if redis_success else 'Failed'}")
            logger.info(f"  - Vault markets: {len(vault_market_keys) if vault else 0}")
            
            return True
            
        except Exception as e:
            logger.error(f"ETL job failed: {e}")
            return False
    
    async def run_with_retry(self, max_retries: int = 3):
        """Run ETL job with retry logic."""
        for attempt in range(max_retries):
            try:
                logger.info(f"ETL job attempt {attempt + 1}/{max_retries}")
                success = await self.run()
                
                if success:
                    logger.info("ETL job completed successfully")
                    return True
                else:
                    logger.warning(f"ETL job failed on attempt {attempt + 1}")
                    
            except Exception as e:
                logger.error(f"ETL job error on attempt {attempt + 1}: {e}")
            
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.info(f"Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)
        
        logger.error("ETL job failed after all retry attempts")
        return False


async def main():
    """Main entry point for the ETL job."""
    logger.info("Starting Morpho ETL Job")
    
    # Validate configuration
    if not config.vault_address:
        logger.error("VAULT_ADDRESS not configured")
        return
    
    if not config.s3_bucket_name:
        logger.error("S3_BUCKET_NAME not configured")
        return
    
    if not config.dynamodb_table_name:
        logger.error("DYNAMODB_TABLE_NAME not configured")
        return
    
    # Run ETL job
    etl_job = MorphoETLJob()
    success = await etl_job.run_with_retry()
    
    if success:
        logger.info("ETL job completed successfully")
        exit(0)
    else:
        logger.error("ETL job failed")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
