#!/bin/bash

# Simple script to update ECS service after Docker images are built
# This script handles the ECS deployment issues mentioned in the README

set -e

# Configuration
AWS_REGION=${AWS_REGION:-eu-central-1}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get Terraform outputs
cd terraform
CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)
BOT_SERVICE_NAME=$(terraform output -raw bot_service_name)
BOT_REPO_URL=$(terraform output -raw ecr_bot_repository_url)
ETL_REPO_URL=$(terraform output -raw ecr_etl_repository_url)
cd ..

log_info "ECS Service Update Script"
log_info "Cluster: $CLUSTER_NAME"
log_info "Service: $BOT_SERVICE_NAME"
log_info "Bot Image: $BOT_REPO_URL:latest"
log_info "ETL Image: $ETL_REPO_URL:latest"

# Check if we should build images
if [ "$1" = "--build-images" ]; then
    log_info "Building and pushing Docker images..."
    
    # Login to ECR
    log_info "Logging into ECR..."
    aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${BOT_REPO_URL%/*}
    
    # Build and push bot image
    log_info "Building bot image..."
    docker build -t ${BOT_REPO_URL}:latest -f bot/Dockerfile .
    docker push ${BOT_REPO_URL}:latest
    
    # Build and push ETL image
    log_info "Building ETL image..."
    docker build -t ${ETL_REPO_URL}:latest -f etl/Dockerfile .
    docker push ${ETL_REPO_URL}:latest
    
    log_info "Docker images built and pushed successfully!"
fi

# Apply any Terraform changes first (this will update task definitions)
log_info "Applying Terraform changes to update task definitions..."
cd terraform
terraform apply -var-file=terraform.tfvars -auto-approve
cd ..

# Force new deployment of bot service
log_info "Forcing new deployment of bot service..."
aws ecs update-service \
    --cluster ${CLUSTER_NAME} \
    --service ${BOT_SERVICE_NAME} \
    --force-new-deployment \
    --region ${AWS_REGION} > /dev/null

# Wait for deployment to complete
log_info "Waiting for deployment to complete..."
aws ecs wait services-stable \
    --cluster ${CLUSTER_NAME} \
    --services ${BOT_SERVICE_NAME} \
    --region ${AWS_REGION}

# Check deployment status
DEPLOYMENT_STATUS=$(aws ecs describe-services \
    --cluster ${CLUSTER_NAME} \
    --services ${BOT_SERVICE_NAME} \
    --region ${AWS_REGION} \
    --query 'services[0].deployments[0].status' \
    --output text)

if [ "$DEPLOYMENT_STATUS" = "PRIMARY" ]; then
    log_info "ECS service deployment completed successfully!"
    
    # Show service information
    echo
    echo "=== Service Information ==="
    echo "Service URL: https://${AWS_REGION}.console.aws.amazon.com/ecs/home?region=${AWS_REGION}#/clusters/${CLUSTER_NAME}/services/${BOT_SERVICE_NAME}/events"
    
    # Show running tasks
    RUNNING_TASKS=$(aws ecs describe-services \
        --cluster ${CLUSTER_NAME} \
        --services ${BOT_SERVICE_NAME} \
        --region ${AWS_REGION} \
        --query 'services[0].runningCount' \
        --output text)
    
    echo "Running Tasks: $RUNNING_TASKS"
    
    # Show latest deployment
    aws ecs describe-services \
        --cluster ${CLUSTER_NAME} \
        --services ${BOT_SERVICE_NAME} \
        --region ${AWS_REGION} \
        --query 'services[0].deployments[0].{Status:status,TaskDefinition:taskDefinition,CreatedAt:createdAt,UpdatedAt:updatedAt}' \
        --output table
        
else
    log_error "ECS service deployment failed. Status: $DEPLOYMENT_STATUS"
    log_info "Check the ECS console for detailed error information:"
    log_info "https://${AWS_REGION}.console.aws.amazon.com/ecs/home?region=${AWS_REGION}#/clusters/${CLUSTER_NAME}/services/${BOT_SERVICE_NAME}/events"
    
    # Show recent service events
    log_info "Recent service events:"
    aws ecs describe-services \
        --cluster ${CLUSTER_NAME} \
        --services ${BOT_SERVICE_NAME} \
        --region ${AWS_REGION} \
        --query 'services[0].events[0:5].{Message:message,CreatedAt:createdAt}' \
        --output table
    
    exit 1
fi

log_info "ECS service update completed!"
