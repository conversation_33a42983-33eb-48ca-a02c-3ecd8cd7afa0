import os
from dotenv import load_dotenv
import requests
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

load_dotenv()
CHAINS_ENV = os.getenv("CHAIN_IDS")
if CHAINS_ENV:
    CHAIN_IDS = [int(value.strip()) for value in CHAINS_ENV.split(',') if value.strip()]
else:
    CHAIN_IDS = [int(os.getenv("CHAIN_ID", "1"))]

VAULT_ADDRESS = os.getenv("VAULT_ADDRESS")
MINIMAL_BORROWED_AMOUNT = float(os.getenv("MINIMAL_BORROWED_AMOUNT", "100000"))

API_URL = "https://api.morpho.org/graphql"

# Helper functions for safe asset access
def safe_get_asset_symbol(asset_data, asset_type="asset", fallback="UNKNOWN"):
    """
    Safely extracts the symbol from an asset object with comprehensive null checking.

    Args:
        asset_data: The asset object (can be None, dict, or other)
        asset_type: Type of asset for logging purposes ("loan" or "collateral")
        fallback: Fallback value when symbol cannot be extracted

    Returns:
        str: The asset symbol or fallback value
    """
    if asset_data is None:
        logging.warning(f"Asset data is None for {asset_type} asset")
        return fallback

    if not isinstance(asset_data, dict):
        logging.warning(f"Asset data is not a dictionary for {asset_type} asset: {type(asset_data)}")
        return fallback

    symbol = asset_data.get('symbol')
    if symbol is None:
        logging.warning(f"Symbol is None for {asset_type} asset: {asset_data}")
        return fallback

    if not isinstance(symbol, str) or not symbol.strip():
        logging.warning(f"Invalid symbol for {asset_type} asset: {symbol}")
        return fallback

    return symbol.strip()

def safe_get_market_assets(market_data):
    """
    Safely extracts both loan and collateral asset symbols from market data.

    Args:
        market_data: Market object from API response

    Returns:
        tuple: (loan_asset_symbol, collateral_asset_symbol)
    """
    if not isinstance(market_data, dict):
        logging.error(f"Market data is not a dictionary: {type(market_data)}")
        return "UNKNOWN", "UNKNOWN"

    loan_asset_data = market_data.get('loanAsset')
    collateral_asset_data = market_data.get('collateralAsset')

    loan_symbol = safe_get_asset_symbol(loan_asset_data, "loan", "UNKNOWN")
    collateral_symbol = safe_get_asset_symbol(collateral_asset_data, "collateral", "UNKNOWN")

    return loan_symbol, collateral_symbol

def validate_market_data(market_data):
    """
    Validates that market data has the expected structure.

    Args:
        market_data: Market object from API response

    Returns:
        bool: True if valid, False otherwise
    """
    if not isinstance(market_data, dict):
        return False

    required_fields = ['uniqueKey']
    for field in required_fields:
        if field not in market_data:
            logging.warning(f"Missing required field '{field}' in market data")
            return False

    return True

VAULT_MARKETS_QUERY = """
query GetMarketsInVault($vaultAddress: String!, $chainId: Int!) {
  vaultByAddress(address: $vaultAddress, chainId: $chainId) {
    chainId
    state {
      allocation {
        market {
          uniqueKey
          chainId
        }
      }
    }
  }
}
"""
WHITELISTED_MARKETS_QUERY = """
query GetAllWhitelistedMarkets(
  $loanId: [String!]
  $collateralId: [String!]
  $chainIds: [Int!]
) {
  markets(
    where: {
      whitelisted: true
      loanAssetId_in: $loanId
      collateralAssetId_in: $collateralId
      chainId_in: $chainIds
    }
  ) {
    items {
      uniqueKey
      chainId
      state {
        supplyApy
        borrowAssetsUsd
        utilization
      }
      loanAsset {
        symbol
        chainId
      }
      collateralAsset {
        symbol
        chainId
      }
    }
  }
}
"""

ASSET_ID_BY_SYMBOL_QUERY = """
query GetAssetIdBySymbol($assetSymbol: String!, $chainIds: [Int!]) {
  assets(where: { symbol_in: [$assetSymbol] chainId_in: $chainIds}) {
    items {
      id
      symbol
      chainId
    }
  }
}
"""

def run_query(query, variables=None):
    """
    Sends a GraphQL query to the API endpoint with enhanced error handling.

    Args:
        query: GraphQL query string
        variables: Optional variables for the query

    Returns:
        dict: JSON response from the API

    Raises:
        requests.exceptions.RequestException: For network-related errors
        ValueError: For invalid JSON responses
    """
    headers = {"Content-Type": "application/json"}
    data = {"query": query}
    if variables:
        data["variables"] = variables

    try:
        logging.info(f"Sending GraphQL query to {API_URL}")
        response = requests.post(API_URL, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        result = response.json()

        # Check for GraphQL errors in the response
        if 'errors' in result:
            logging.error(f"GraphQL errors in response: {result['errors']}")

        if 'data' not in result:
            logging.warning("No 'data' field in GraphQL response")

        return result

    except requests.exceptions.Timeout:
        logging.error("Request timeout when calling Morpho API")
        raise
    except requests.exceptions.ConnectionError:
        logging.error("Connection error when calling Morpho API")
        raise
    except requests.exceptions.HTTPError as e:
        logging.error(f"HTTP error when calling Morpho API: {e}")
        raise
    except json.JSONDecodeError as e:
        logging.error(f"Invalid JSON response from Morpho API: {e}")
        raise ValueError(f"Invalid JSON response: {e}")
    except Exception as e:
        logging.error(f"Unexpected error when calling Morpho API: {e}")
        raise

def get_asset_ids_by_symbol(asset_symbol):
    """
    Returns all asset IDs for a given asset symbol.

    Args:
        asset_symbol: The asset symbol to look up (e.g., "USDC", "WETH")

    Returns:
        list: List of asset IDs for the given symbol, empty list if none found or error occurs
    """
    try:
        logging.info(f"Fetching asset IDs for symbol: {asset_symbol}")
        asset_data = run_query(ASSET_ID_BY_SYMBOL_QUERY, {
            "assetSymbol": asset_symbol,
            "chainIds": CHAIN_IDS
        })

        if not asset_data or 'data' not in asset_data:
            logging.error("Invalid response structure from asset ID query")
            return []

        data = asset_data.get('data', {})
        assets = data.get('assets', {})
        items = assets.get('items', [])

        if not isinstance(items, list):
            logging.error(f"Assets items is not a list: {type(items)}")
            return []

        asset_ids = []
        for asset in items:
            if isinstance(asset, dict) and 'id' in asset:
                asset_ids.append(asset['id'])
            else:
                logging.warning(f"Invalid asset data: {asset}")

        logging.info(f"Found {len(asset_ids)} asset IDs for symbol {asset_symbol}")
        return asset_ids

    except Exception as e:
        logging.error(f"Error fetching asset IDs for symbol {asset_symbol}: {e}")
        return []

def get_vault_markets(vault_address):
    """
    Returns the set of unique market keys in the vault with enhanced error handling.

    Args:
        vault_address: The vault address to query

    Returns:
        set or None: Set of unique market keys, or None if vault not found
    """
    try:
        logging.info(f"Fetching markets for vault: {vault_address}")
        all_unique_keys = set()
        found_chain = False

        for chain_id in CHAIN_IDS:
            vault_data = run_query(VAULT_MARKETS_QUERY, {
                "vaultAddress": vault_address,
                "chainId": chain_id
            })

            if not vault_data or 'data' not in vault_data:
                logging.error("Invalid response structure from vault markets query")
                continue

            vault_by_address = vault_data.get('data', {}).get('vaultByAddress')
            if not vault_by_address:
                logging.info(f"Vault not found for address {vault_address} on chain {chain_id}")
                continue

            found_chain = True

            state = vault_by_address.get('state', {})
            allocation = state.get('allocation', [])

            if not isinstance(allocation, list):
                logging.error(f"Allocation is not a list on chain {chain_id}: {type(allocation)}")
                continue

            for item in allocation:
                if not isinstance(item, dict):
                    logging.warning(f"Allocation item is not a dict: {type(item)}")
                    continue

                market = item.get('market', {})
                if not isinstance(market, dict):
                    logging.warning(f"Market is not a dict: {type(market)}")
                    continue

                unique_key = market.get('uniqueKey')
                if unique_key:
                    all_unique_keys.add(unique_key)
                else:
                    logging.warning("Market missing uniqueKey")

        if not found_chain:
            logging.warning(f"Vault not found for address: {vault_address}")
            return None

        logging.info(f"Found {len(all_unique_keys)} markets in vault across chains {CHAIN_IDS}")
        return all_unique_keys

    except Exception as e:
        logging.error(f"Error fetching vault markets: {e}")
        return None

def get_whitelisted_markets(loan_symbols=None, collateral_symbols=None):
    """
    Returns whitelisted markets as a list of dicts with enhanced validation and optional filtering.

    Args:
        loan_symbols: Optional list of loan asset symbols to filter by
        collateral_symbols: Optional list of collateral asset symbols to filter by

    Returns:
        list: List of market dictionaries, empty list if error occurs
    """
    try:
        # Prepare variables for the GraphQL query
        variables = {
            "chainIds": CHAIN_IDS
        }

        # Resolve loan symbols to IDs if provided
        if loan_symbols:
            loan_ids = []
            for symbol in loan_symbols:
                ids = get_asset_ids_by_symbol(symbol)
                loan_ids.extend(ids)
            if loan_ids:
                variables['loanId'] = loan_ids
                logging.info(f"Filtering by loan asset IDs: {loan_ids}")

        # Resolve collateral symbols to IDs if provided
        if collateral_symbols:
            collateral_ids = []
            for symbol in collateral_symbols:
                ids = get_asset_ids_by_symbol(symbol)
                collateral_ids.extend(ids)
            if collateral_ids:
                variables['collateralId'] = collateral_ids
                logging.info(f"Filtering by collateral asset IDs: {collateral_ids}")

        logging.info("Fetching whitelisted markets")
        whitelisted_data = run_query(WHITELISTED_MARKETS_QUERY, variables if variables else None)

        if not whitelisted_data or 'data' not in whitelisted_data:
            logging.error("Invalid response structure from whitelisted markets query")
            return []

        data = whitelisted_data.get('data', {})
        markets = data.get('markets', {})
        items = markets.get('items', [])

        if not isinstance(items, list):
            logging.error(f"Markets items is not a list: {type(items)}")
            return []

        # Validate each market item
        valid_markets = []
        for i, market in enumerate(items):
            if validate_market_data(market):
                valid_markets.append(market)
            else:
                logging.warning(f"Invalid market data at index {i}: {market}")

        logging.info(f"Found {len(valid_markets)} valid whitelisted markets")
        return valid_markets

    except Exception as e:
        logging.error(f"Error fetching whitelisted markets: {e}")
        return []

def get_new_whitelisted_markets_for_vault(vault_address=None, loan_asset_filter=None, collateral_asset_filter=None):
    """
    Returns a formatted string of new whitelisted markets not yet in the vault.
    Optionally filters by loan_asset and/or collateral_asset.

    Args:
        vault_address: Vault address to check against
        loan_asset_filter: Optional loan asset symbol to filter by (for backward compatibility)
        collateral_asset_filter: Optional collateral asset symbol to filter by (for backward compatibility)
    """
    if vault_address is None:
        vault_address = VAULT_ADDRESS

    if not vault_address:
        return "❌ No vault address provided. Please set VAULT_ADDRESS in your .env file."

    try:
        vault_markets_unique_keys = get_vault_markets(vault_address)
        if vault_markets_unique_keys is None:
            return f"❌ Vault with address {vault_address} not found or could not be accessed."

        # Prepare filter lists for the new API
        loan_symbols = [loan_asset_filter] if loan_asset_filter else None
        collateral_symbols = [collateral_asset_filter] if collateral_asset_filter else None

        all_whitelisted_markets = get_whitelisted_markets(
            loan_symbols=loan_symbols,
            collateral_symbols=collateral_symbols
        )
        if not all_whitelisted_markets:
            return "❌ No whitelisted markets could be retrieved from the API."

        # Filter new markets (markets not yet in the vault)
        filtered_markets = [
            market for market in all_whitelisted_markets
            if market.get('uniqueKey') not in vault_markets_unique_keys
        ]

        if filtered_markets:
            market_tuples = []

            for market in filtered_markets:
                unique_key = market.get('uniqueKey', 'N/A')
                state = market.get('state', {})

                # Extract all state values with safe defaults
                supply_apy_val = None
                borrow_assets_usd_val = None
                utilization_val = None

                if isinstance(state, dict):
                    supply_apy_val = state.get('supplyApy')
                    borrow_assets_usd_val = state.get('borrowAssetsUsd')
                    utilization_val = state.get('utilization')

                try:
                    borrow_numeric = float(borrow_assets_usd_val)
                except (TypeError, ValueError):
                    borrow_numeric = None

                if borrow_numeric is None or borrow_numeric < MINIMAL_BORROWED_AMOUNT:
                    continue

                loan_asset, collateral_asset = safe_get_market_assets(market)
                chain_id = market.get('chainId')
                if chain_id is None:
                    chain_id = 'N/A'

                # Format supply APY
                try:
                    supply_numeric = float(supply_apy_val) if supply_apy_val is not None else None
                except (ValueError, TypeError):
                    supply_numeric = None

                if supply_numeric is not None:
                    supply_apy = f"{supply_numeric * 100:.2f}%"
                    apy_sort_val = supply_numeric
                else:
                    supply_apy = "N/A"
                    apy_sort_val = -1

                # Format borrowed amount
                if borrow_numeric is not None:
                    borrow_assets_usd = f"${borrow_numeric:,.2f}"
                else:
                    borrow_assets_usd = "N/A"

                # Format utilization
                try:
                    utilization_numeric = float(utilization_val) if utilization_val is not None else None
                except (ValueError, TypeError):
                    utilization_numeric = None

                if utilization_numeric is not None:
                    utilization = f"{utilization_numeric * 100:.2f}%"
                else:
                    utilization = "N/A"

                chain_label = f"Chain {chain_id}" if chain_id != 'N/A' else "Chain N/A"
                market_line = (f"• {chain_label} | {collateral_asset}/{loan_asset} | "
                               f"Supply APY {supply_apy} | "
                               f"Borrowed {borrow_assets_usd} | "
                               f"Utilization {utilization} | "
                               f"Key {unique_key}")
                market_tuples.append((apy_sort_val, market_line))

            if not market_tuples:
                return "✅ No new whitelisted markets found for your vault with the given filters."

            market_tuples.sort(reverse=True, key=lambda x: x[0])
            chain_summary = ", ".join(str(c) for c in CHAIN_IDS)
            header = f"✅ New whitelisted markets not yet in your vault ({len(market_tuples)} total, chains: {chain_summary})"

            result_lines = [header]
            result_lines.extend(line for _, line in market_tuples)

            return "\n".join(result_lines)
        else:
            return "✅ No new whitelisted markets found for your vault with the given filters."

    except requests.exceptions.RequestException as e:
        return f"❌ Network error occurred while fetching data: {e}"
    except (KeyError, TypeError) as e:
        return f"❌ Data parsing error - API response structure may have changed: {e}"
    except Exception as e:
        return f"❌ Unexpected error occurred: {e}"

def get_new_whitelisted_markets_for_vault_flexible(vault_address=None, loan_symbols=None, collateral_symbols=None):
    """
    Enhanced version that accepts lists of symbols for more flexible filtering.

    Args:
        vault_address: Vault address to check against
        loan_symbols: Optional list of loan asset symbols to filter by
        collateral_symbols: Optional list of collateral asset symbols to filter by

    Returns:
        str: Formatted string of new whitelisted markets
    """
    if vault_address is None:
        vault_address = VAULT_ADDRESS

    if not vault_address:
        return "❌ No vault address provided. Please set VAULT_ADDRESS in your .env file."

    try:
        vault_markets_unique_keys = get_vault_markets(vault_address)
        if vault_markets_unique_keys is None:
            return f"❌ Vault with address {vault_address} not found or could not be accessed."

        all_whitelisted_markets = get_whitelisted_markets(
            loan_symbols=loan_symbols,
            collateral_symbols=collateral_symbols
        )
        if not all_whitelisted_markets:
            return "❌ No whitelisted markets could be retrieved from the API."

        # Filter new markets (markets not yet in the vault)
        filtered_markets = [
            market for market in all_whitelisted_markets
            if market.get('uniqueKey') not in vault_markets_unique_keys
        ]

        if filtered_markets:
            market_tuples = []

            for market in filtered_markets:
                unique_key = market.get('uniqueKey', 'N/A')
                state = market.get('state', {})

                # Extract all state values with safe defaults
                supply_apy_val = None
                borrow_assets_usd_val = None
                utilization_val = None

                if isinstance(state, dict):
                    supply_apy_val = state.get('supplyApy')
                    borrow_assets_usd_val = state.get('borrowAssetsUsd')
                    utilization_val = state.get('utilization')

                try:
                    borrow_numeric = float(borrow_assets_usd_val)
                except (TypeError, ValueError):
                    borrow_numeric = None

                if borrow_numeric is None or borrow_numeric < MINIMAL_BORROWED_AMOUNT:
                    continue

                loan_asset, collateral_asset = safe_get_market_assets(market)

                # Format supply APY
                try:
                    supply_numeric = float(supply_apy_val) if supply_apy_val is not None else None
                except (ValueError, TypeError):
                    supply_numeric = None

                if supply_numeric is not None:
                    supply_apy = f"{supply_numeric * 100:.2f}%"
                    apy_sort_val = supply_numeric
                else:
                    supply_apy = "N/A"
                    apy_sort_val = -1

                # Format borrowed amount
                if borrow_numeric is not None:
                    borrow_assets_usd = f"${borrow_numeric:,.2f}"
                else:
                    borrow_assets_usd = "N/A"

                # Format utilization
                try:
                    utilization_numeric = float(utilization_val) if utilization_val is not None else None
                except (ValueError, TypeError):
                    utilization_numeric = None

                if utilization_numeric is not None:
                    utilization = f"{utilization_numeric * 100:.2f}%"
                else:
                    utilization = "N/A"

                chain_id = market.get('chainId')
                if chain_id is None:
                    chain_id = 'N/A'

                chain_label = f"Chain {chain_id}" if chain_id != 'N/A' else "Chain N/A"
                market_line = (f"• {chain_label} | {collateral_asset}/{loan_asset} | "
                               f"Supply APY {supply_apy} | "
                               f"Borrowed {borrow_assets_usd} | "
                               f"Utilization {utilization} | "
                               f"Key {unique_key}")
                market_tuples.append((apy_sort_val, market_line))

            if not market_tuples:
                return "✅ No new whitelisted markets found for your vault with the given filters."

            market_tuples.sort(reverse=True, key=lambda x: x[0])
            chain_summary = ", ".join(str(c) for c in CHAIN_IDS)
            header = f"✅ New whitelisted markets not yet in your vault ({len(market_tuples)} total, chains: {chain_summary})"

            result_lines = [header]
            result_lines.extend(line for _, line in market_tuples)

            return "\n".join(result_lines)
        else:
            return "✅ No new whitelisted markets found for your vault with the given filters."

    except requests.exceptions.RequestException as e:
        return f"❌ Network error occurred while fetching data: {e}"
    except (KeyError, TypeError) as e:
        return f"❌ Data parsing error - API response structure may have changed: {e}"
    except Exception as e:
        return f"❌ Unexpected error occurred: {e}"


if __name__ == "__main__":
    collateral_filter = input("Enter collateral asset filter (or press Enter to skip): ").strip() or None
    loan_filter = input("Enter loan asset filter (or press Enter to skip): ").strip() or None
    print(get_new_whitelisted_markets_for_vault(collateral_asset_filter=collateral_filter, loan_asset_filter=loan_filter))