#!/bin/bash

# Morpho Helper Deployment Script
# This script deploys the Morpho Helper application to AWS

set -e

# Configuration
ENVIRONMENT=${1:-dev}
AWS_REGION=${AWS_REGION:-us-east-1}
PROJECT_NAME="morpho-helper"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install it first."
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured. Please run 'aws configure'."
        exit 1
    fi
    
    log_info "Prerequisites check passed!"
}

# Deploy infrastructure with Terraform
deploy_infrastructure() {
    log_info "Deploying infrastructure with Terraform..."
    
    cd terraform
    
    # Initialize Terraform
    terraform init
    
    # TODO: Maybe something more robust?
    # TODO: Don't like dotenv variables being here (maintainence issue)
    # Create terraform.tfvars if it doesn't exist
    if [ ! -f terraform.tfvars ]; then
        log_warn "Creating terraform.tfvars file. Please review and update it."
        cat > terraform.tfvars << EOF
environment = "${ENVIRONMENT}"
aws_region = "${AWS_REGION}"
project_name = "${PROJECT_NAME}"

# Update these values as needed
vault_address = "******************************************"
minimal_borrowed_amount = 100000

# Resource sizing (adjust for production)
bot_cpu = 256
bot_memory = 512
etl_cpu = 1024
etl_memory = 2048

# Cost optimization
enable_spot_instances = true
enable_nat_gateway = true
enable_vpc_endpoints = true

# Security
enable_deletion_protection = false
EOF
    fi
    
    # Plan and apply
    terraform plan -var-file=terraform.tfvars
    
    read -p "Do you want to apply these changes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        terraform apply -var-file=terraform.tfvars -auto-approve
        log_info "Infrastructure deployment completed!"
    else
        log_warn "Infrastructure deployment cancelled."
        exit 1
    fi
    
    cd ..
}

# Build and push Docker images
build_and_push_images() {
    log_info "Building and pushing Docker images..."

    local builder_name="morpho-helper-builder-$(date +%s)"
    local cleanup_cmd="docker buildx use default >/dev/null 2>&1 || true; docker buildx rm \"${builder_name}\" >/dev/null 2>&1 || true; trap - RETURN; trap - EXIT"

    docker buildx create --name "${builder_name}" --use >/dev/null
    trap "${cleanup_cmd}" RETURN
    trap "${cleanup_cmd}" EXIT

    # Get ECR repository URLs from Terraform output
    cd terraform
    BOT_REPO_URL=$(terraform output -raw ecr_bot_repository_url)
    ETL_REPO_URL=$(terraform output -raw ecr_etl_repository_url)
    cd ..
    
    # Login to ECR
    aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${BOT_REPO_URL%/*}

    # Build and push bot image
    log_info "Building bot image..."
    docker buildx build --push -t ${BOT_REPO_URL}:latest -f bot/Dockerfile .

    # Build and push ETL image
    log_info "Building ETL image..."
    docker buildx build --push -t ${ETL_REPO_URL}:latest -f etl/Dockerfile .

    log_info "Docker images built and pushed successfully!"
}

# Set up secrets
setup_secrets() {
    log_info "Setting up secrets..."

    # Check if Telegram token is set
    if [ -z "$TELEGRAM_TOKEN" ]; then
        if [ -f ".env" ]; then
            TELEGRAM_TOKEN=$(grep "TELEGRAM_TOKEN=" .env | cut -d'=' -f2)
        fi
    fi

    if [ -z "$TELEGRAM_TOKEN" ]; then
        log_error "Telegram token is required! Set TELEGRAM_TOKEN environment variable or add it to .env file"
        exit 1
    fi

    # Update Telegram token in Secrets Manager
    SECRET_NAME="${PROJECT_NAME}-${ENVIRONMENT}/telegram-token"
    aws secretsmanager update-secret \
        --secret-id "$SECRET_NAME" \
        --secret-string "$TELEGRAM_TOKEN" \
        --region ${AWS_REGION} 2>/dev/null || \
    aws secretsmanager create-secret \
        --name "$SECRET_NAME" \
        --secret-string "$TELEGRAM_TOKEN" \
        --region ${AWS_REGION}

    # Set up SNS email subscriptions programmatically
    cd terraform
    SNS_TOPIC_ARN=$(terraform output -raw sns_alerts_topic_arn)
    cd ..

    # Add team email subscriptions
    TEAM_EMAILS="<EMAIL> <EMAIL> <EMAIL>"
    for email in $TEAM_EMAILS; do
        log_info "Adding SNS subscription for $email"
        aws sns subscribe \
            --topic-arn "$SNS_TOPIC_ARN" \
            --protocol email \
            --notification-endpoint "$email" \
            --region ${AWS_REGION} 2>/dev/null || log_warn "Failed to subscribe $email (may already be subscribed)"
    done

    log_info "Secrets and notifications configured successfully!"
}

# Update ECS service
update_services() {
    log_info "Updating ECS services..."
    
    cd terraform
    CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)
    BOT_SERVICE_NAME=$(terraform output -raw bot_service_name)
    cd ..
    
    # Force new deployment of bot service
    aws ecs update-service \
        --cluster ${CLUSTER_NAME} \
        --service ${BOT_SERVICE_NAME} \
        --force-new-deployment \
        --region ${AWS_REGION}
    
    log_info "ECS service update initiated!"
}

# Run initial ETL job
run_initial_etl() {
    log_info "Running initial ETL job..."
    
    cd terraform
    JOB_QUEUE=$(terraform output -raw batch_job_queue_name)
    JOB_DEFINITION=$(terraform output -raw batch_job_definition_name)
    cd ..
    
    # Submit ETL job
    JOB_ID=$(aws batch submit-job \
        --job-name "initial-etl-$(date +%s)" \
        --job-queue ${JOB_QUEUE} \
        --job-definition ${JOB_DEFINITION} \
        --region ${AWS_REGION} \
        --query 'jobId' \
        --output text)
    
    log_info "ETL job submitted with ID: ${JOB_ID}"
    log_info "You can monitor the job in the AWS Batch console."
}

# Main deployment function
main() {
    log_info "Starting deployment for environment: ${ENVIRONMENT}"
    
    check_prerequisites
    deploy_infrastructure
    setup_secrets
    build_and_push_images
    update_services
    run_initial_etl
    
    log_info "Deployment completed successfully!"
    log_info "Check the AWS console for service status and logs."
    
    # Show useful information
    cd terraform
    echo
    echo "=== Deployment Information ==="
    echo "Dashboard URL: $(terraform output -raw cloudwatch_dashboard_url)"
    echo "S3 Bucket: $(terraform output -raw s3_bucket_name)"
    echo "DynamoDB Table: $(terraform output -raw dynamodb_table_name)"
    echo "Redis Endpoint: $(terraform output -raw redis_endpoint)"
    cd ..
}

# Run main function
main "$@"
