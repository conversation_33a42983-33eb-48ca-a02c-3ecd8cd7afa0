#!/bin/bash

# Deploy bot fix to ECS
# This script builds and pushes the updated bot image, then forces a new deployment

set -e

echo "🚀 Deploying Morpho Helper Bot Fix..."

# Configuration
REGION="eu-central-1"
ACCOUNT_ID="************"
REPO_NAME="morpho-helper-prod/bot"
CLUSTER_NAME="morpho-helper-prod-cluster"
SERVICE_NAME="morpho-helper-prod-bot"
IMAGE_TAG="latest"

# Full image URI
IMAGE_URI="${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${REPO_NAME}:${IMAGE_TAG}"

echo "📦 Building Docker image..."
docker build -t ${REPO_NAME}:${IMAGE_TAG} -f bot/Dockerfile .

echo "🔐 Logging into ECR..."
aws ecr get-login-password --region ${REGION} | docker login --username AWS --password-stdin ${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com

echo "🏷️  Tagging image..."
docker tag ${REPO_NAME}:${IMAGE_TAG} ${IMAGE_URI}

echo "⬆️  Pushing image to ECR..."
docker push ${IMAGE_URI}

echo "🔄 Forcing ECS service deployment..."
aws ecs update-service \
    --cluster ${CLUSTER_NAME} \
    --service ${SERVICE_NAME} \
    --force-new-deployment \
    --region ${REGION}

echo "✅ Deployment initiated! Monitoring service status..."

# Wait for deployment to complete
echo "⏳ Waiting for service to stabilize..."
aws ecs wait services-stable \
    --cluster ${CLUSTER_NAME} \
    --services ${SERVICE_NAME} \
    --region ${REGION}

echo "🎉 Bot fix deployed successfully!"
echo "📊 Checking service status..."

aws ecs describe-services \
    --cluster ${CLUSTER_NAME} \
    --services ${SERVICE_NAME} \
    --region ${REGION} \
    --query 'services[0].[serviceName,runningCount,pendingCount,desiredCount]' \
    --output table

echo "📝 Recent service events:"
aws ecs describe-services \
    --cluster ${CLUSTER_NAME} \
    --services ${SERVICE_NAME} \
    --region ${REGION} \
    --query 'services[0].events[0:3]' \
    --output table

echo "✅ Deployment complete! Check CloudWatch logs for bot startup status."
