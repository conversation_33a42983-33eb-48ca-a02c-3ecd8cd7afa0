#!/usr/bin/env python3
"""
Quick verification script to test the bot's core functionality
without sending actual Telegram messages.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bot.services import MarketService
from shared.config import config

async def test_bot_functionality():
    """Test the bot's core functionality."""
    print("🔧 Testing Bot Core Functionality")
    print("=" * 50)
    
    try:
        # Initialize the market service
        market_service = MarketService()
        
        # Test 1: Status check
        print("📊 Testing status check...")
        status_info = await market_service.get_status_info()
        print(f"Status response length: {len(status_info)} characters")
        
        # Check if DynamoDB is now connected
        if "✅ Connected" in status_info and "DynamoDB" in status_info:
            print("✅ DynamoDB connectivity: FIXED")
        elif "❌ Disconnected" in status_info and "DynamoDB" in status_info:
            print("❌ DynamoDB connectivity: STILL FAILING")
        else:
            print("⚠️ DynamoDB status unclear")
        
        # Test 2: Market data retrieval
        print("\n📈 Testing market data retrieval...")
        markets = await market_service._get_markets_with_fallback()
        print(f"Retrieved {len(markets)} markets")
        
        if len(markets) > 0:
            print("✅ Market data retrieval: WORKING")
            
            # Test 3: Asset filtering
            print("\n🔍 Testing USDC filtering...")
            usdc_markets = await market_service._get_markets_with_fallback(loan_asset_filter="USDC")
            print(f"USDC markets: {len(usdc_markets)}")
            
            if len(usdc_markets) > 0:
                print("✅ Asset filtering: WORKING")
            else:
                print("❌ Asset filtering: NO RESULTS")
        else:
            print("❌ Market data retrieval: FAILING")
        
        print("\n" + "=" * 50)
        print("🎯 VERIFICATION SUMMARY")
        print("=" * 50)
        
        if "✅ Connected" in status_info and len(markets) > 0:
            print("🎉 SUCCESS: Bot functionality is RESTORED!")
            print(f"   - DynamoDB connectivity: FIXED")
            print(f"   - Market data available: {len(markets)} markets")
            print(f"   - Asset filtering working: {len(usdc_markets) if 'usdc_markets' in locals() else 'N/A'} USDC markets")
            return True
        else:
            print("❌ FAILURE: Bot still has issues")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_bot_functionality())
    sys.exit(0 if success else 1)
