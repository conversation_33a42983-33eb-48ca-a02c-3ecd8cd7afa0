{"taskDefinitionArn": "arn:aws:ecs:eu-central-1:236840575805:task-definition/morpho-helper-prod-bot:2", "containerDefinitions": [{"name": "bot", "image": "236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-helper-bot:latest", "cpu": 0, "portMappings": [{"containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DYNAMODB_TABLE_NAME", "value": "morpho-helper-prod-markets"}, {"name": "REDIS_CLUSTER_ENDPOINT", "value": "master.morpho-helper-prod-redis.dejc4c.euc1.cache.amazonaws.com:6379"}, {"name": "AWS_REGION", "value": "eu-central-1"}, {"name": "SERVICE_TYPE", "value": "bot"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "TELEGRAM_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-central-1:236840575805:secret:morpho-helper/telegram-token-SaFMJX"}, {"name": "REDIS_AUTH_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-central-1:236840575805:secret:morpho-helper-prod/redis-auth-token-wlKDR2"}, {"name": "VAULT_ADDRESS", "valueFrom": "arn:aws:secretsmanager:eu-central-1:236840575805:secret:morpho-helper-prod/app-config-UncjEw:vault_address::"}, {"name": "MINIMAL_BORROWED_AMOUNT", "valueFrom": "arn:aws:secretsmanager:eu-central-1:236840575805:secret:morpho-helper-prod/app-config-UncjEw:minimal_borrowed_amount::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/morpho-helper-prod-bot", "awslogs-region": "eu-central-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "systemControls": []}], "family": "morpho-helper-prod-bot", "taskRoleArn": "arn:aws:iam::236840575805:role/morpho-helper-prod-bot-task-role", "executionRoleArn": "arn:aws:iam::236840575805:role/morpho-helper-prod-ecs-task-execution-role", "networkMode": "awsvpc", "revision": 2, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.container-health-check"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "registeredAt": "2025-09-10T10:33:36.238000+02:00", "registeredBy": "arn:aws:iam::236840575805:user/<EMAIL>"}