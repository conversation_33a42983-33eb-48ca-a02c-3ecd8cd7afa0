#!/bin/bash

# Local Development Setup Script for Morpho Helper

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_info "Prerequisites check passed!"
}

# Setup environment file
setup_env() {
    log_info "Setting up environment file..."
    
    if [ ! -f .env ]; then
        log_warn ".env file not found. Creating from template..."
        cp .env .env.backup 2>/dev/null || true
        
        cat > .env << EOF
# Morpho Helper Local Development Configuration

# Telegram Bot Token (required)
TELEGRAM_TOKEN=

# Morpho Configuration
CHAIN_ID=1
VAULT_ADDRESS=0x62fE596d59fB077c2Df736dF212E0AFfb522dC78
MINIMAL_BORROWED_AMOUNT=100000

# Local Development Settings
AWS_REGION=us-east-1
DYNAMODB_TABLE_NAME=morpho-dev-markets
S3_BUCKET_NAME=morpho-dev-data-lake
REDIS_CLUSTER_ENDPOINT=redis:6379
REDIS_AUTH_TOKEN=devpassword

# AWS Local Development (for LocalStack/MinIO)
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_ENDPOINT_URL_DYNAMODB=http://localhost:8000
AWS_ENDPOINT_URL_S3=http://localhost:9000
EOF
        
        log_warn "Please edit .env file and add your TELEGRAM_TOKEN"
    fi
    
    # Check if TELEGRAM_TOKEN is set
    source .env
    if [ -z "$TELEGRAM_TOKEN" ]; then
        log_error "TELEGRAM_TOKEN is not set in .env file. Please add it."
        exit 1
    fi
    
    log_info "Environment configuration ready!"
}

# Initialize local services
init_services() {
    log_info "Initializing local services..."
    
    # Start infrastructure services
    docker-compose up -d redis dynamodb minio
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Create DynamoDB table
    log_info "Creating DynamoDB table..."
    aws dynamodb create-table \
        --table-name morpho-dev-markets \
        --attribute-definitions \
            AttributeName=PK,AttributeType=S \
            AttributeName=SK,AttributeType=S \
            AttributeName=loan_asset_symbol,AttributeType=S \
            AttributeName=collateral_asset_symbol,AttributeType=S \
            AttributeName=supply_apy,AttributeType=N \
            AttributeName=whitelisted,AttributeType=S \
        --key-schema \
            AttributeName=PK,KeyType=HASH \
            AttributeName=SK,KeyType=RANGE \
        --global-secondary-indexes \
            'IndexName=AssetFilterIndex,KeySchema=[{AttributeName=loan_asset_symbol,KeyType=HASH},{AttributeName=collateral_asset_symbol,KeyType=RANGE}],Projection={ProjectionType=ALL},BillingMode=PAY_PER_REQUEST' \
            'IndexName=APYRankingIndex,KeySchema=[{AttributeName=whitelisted,KeyType=HASH},{AttributeName=supply_apy,KeyType=RANGE}],Projection={ProjectionType=ALL},BillingMode=PAY_PER_REQUEST' \
        --billing-mode PAY_PER_REQUEST \
        --endpoint-url http://localhost:8000 \
        --region us-east-1 2>/dev/null || log_warn "DynamoDB table might already exist"
    
    # Create S3 bucket in MinIO
    log_info "Creating S3 bucket..."
    aws s3 mb s3://morpho-dev-data-lake \
        --endpoint-url http://localhost:9000 \
        --region us-east-1 2>/dev/null || log_warn "S3 bucket might already exist"
    
    log_info "Local services initialized!"
}

# Run ETL job once
run_etl() {
    log_info "Running ETL job..."
    docker-compose run --rm etl
    log_info "ETL job completed!"
}

# Start bot service
start_bot() {
    log_info "Starting bot service..."
    docker-compose up bot
}

# Show status
show_status() {
    log_info "Service Status:"
    docker-compose ps
    
    echo
    log_info "Available endpoints:"
    echo "  - Bot health check: http://localhost:8080/health"
    echo "  - Redis: localhost:6379 (password: devpassword)"
    echo "  - DynamoDB: http://localhost:8000"
    echo "  - MinIO Console: http://localhost:9001 (admin/admin)"
    echo "  - MinIO API: http://localhost:9000"
}

# Clean up
cleanup() {
    log_info "Cleaning up local environment..."
    docker-compose down -v
    docker system prune -f
    log_info "Cleanup completed!"
}

# Show help
show_help() {
    echo "Morpho Helper Local Development Script"
    echo
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  setup     - Set up local development environment"
    echo "  start     - Start all services"
    echo "  bot       - Start only the bot service"
    echo "  etl       - Run ETL job once"
    echo "  status    - Show service status"
    echo "  logs      - Show logs for all services"
    echo "  cleanup   - Clean up local environment"
    echo "  help      - Show this help message"
    echo
    echo "Examples:"
    echo "  $0 setup    # Initial setup"
    echo "  $0 start    # Start all services"
    echo "  $0 etl      # Run ETL job"
    echo "  $0 cleanup  # Clean up"
}

# Main function
main() {
    case "${1:-setup}" in
        setup)
            check_prerequisites
            setup_env
            init_services
            run_etl
            show_status
            log_info "Setup completed! Run '$0 bot' to start the bot service."
            ;;
        start)
            docker-compose up
            ;;
        bot)
            start_bot
            ;;
        etl)
            run_etl
            ;;
        status)
            show_status
            ;;
        logs)
            docker-compose logs -f
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
