"""
Main entry point for the Telegram bot service.
"""
import os
import logging
import asyncio
from aiohttp import web
from telegram.ext import <PERSON><PERSON><PERSON>er, CommandHandler

from shared.config import config
from bot.handlers import BotHandlers
from bot.services import MarketService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def health_check(request):
    """Health check endpoint for load balancer."""
    return web.json_response({"status": "healthy", "service": "morpho-bot"})


async def start_health_server():
    """Start health check server."""
    app = web.Application()
    app.router.add_get('/health', health_check)

    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 8080)
    await site.start()
    logger.info("Health check server started on port 8080")


def main():
    """Main function to start the bot."""
    # Set service type for configuration validation
    os.environ["SERVICE_TYPE"] = "bot"

    # Validate configuration
    if not config.telegram_token:
        logger.error("TELEGRAM_TOKEN not configured")
        return

    if not config.redis_cluster_endpoint:
        logger.error("REDIS_CLUSTER_ENDPOINT not configured")
        return

    logger.info("Starting Morpho Helper Telegram Bot")
    logger.info(f"Redis endpoint: {config.redis_cluster_endpoint}")
    logger.info(f"DynamoDB table: {config.dynamodb_table_name}")

    # Initialize services
    market_service = MarketService()
    handlers = BotHandlers(market_service)

    # Build application
    app = ApplicationBuilder().token(config.telegram_token).build()

    # Add handlers
    app.add_handler(CommandHandler("start", handlers.start_command))
    app.add_handler(CommandHandler("help", handlers.help_command))
    app.add_handler(CommandHandler("status", handlers.status_command))

    logger.info("Bot handlers registered")
    logger.info("Available commands: /start, /help, /status")

    # Start health check server as a background task
    async def post_init(application):
        """Post-initialization hook to start health server."""
        await start_health_server()

    # Set up the post-init hook
    app.post_init = post_init

    # Start the bot - this manages its own event loop
    try:
        logger.info("Starting bot polling...")
        app.run_polling()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")
        raise


if __name__ == "__main__":
    main()
