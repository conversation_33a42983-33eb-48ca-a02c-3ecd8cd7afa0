#!/usr/bin/env python3
"""
Production CLI tool for market analytics and trend analysis.

This tool provides advanced analytics capabilities for historical Morpho market data,
including trend analysis, performance metrics, and market comparisons.
"""

import argparse
import boto3
import json
import sys
import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.config import config


class MarketAnalytics:
    """Production tool for market analytics and trend analysis."""
    
    def __init__(self, region: str = None, bucket: str = None):
        self.region = region or config.aws_region
        self.bucket = bucket or config.s3_bucket_name
        self.s3_client = boto3.client('s3', region_name=self.region)
    
    def analyze_market_trends(self, market_key: str, hours: int = 24) -> Dict:
        """Analyze trends for a specific market."""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)
        
        # Get market data
        data = self._get_market_data(market_key, start_time, end_time)
        
        if not data:
            return {
                'status': 'error',
                'message': f'No data found for market {market_key}',
                'market_key': market_key
            }
        
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['query_timestamp'])
        df = df.sort_values('timestamp')
        
        # Calculate trends
        trends = self._calculate_trends(df)
        
        return {
            'status': 'success',
            'market_key': market_key,
            'time_range': f"{start_time.isoformat()} to {end_time.isoformat()}",
            'data_points': len(df),
            'trends': trends,
            'current_state': {
                'supply_apy': float(df.iloc[-1]['supply_apy']) if not df.empty else None,
                'borrow_assets_usd': float(df.iloc[-1]['borrow_assets_usd']) if not df.empty else None,
                'utilization': float(df.iloc[-1]['utilization']) if not df.empty else None,
                'loan_asset': df.iloc[-1]['loan_asset_symbol'] if not df.empty else None,
                'collateral_asset': df.iloc[-1]['collateral_asset_symbol'] if not df.empty else None
            }
        }
    
    def get_top_markets(self, metric: str = 'supply_apy', limit: int = 10, 
                       asset_filter: str = None, hours: int = 1) -> Dict:
        """Get top performing markets by specified metric."""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)
        
        # Get recent data
        all_data = self._get_all_recent_data(start_time, end_time, asset_filter)
        
        if not all_data:
            return {
                'status': 'error',
                'message': 'No recent data found',
                'metric': metric
            }
        
        df = pd.DataFrame(all_data)
        
        # Group by market and get latest values
        latest_data = df.groupby('unique_key').last().reset_index()
        
        # Sort by metric
        if metric in latest_data.columns:
            top_markets = latest_data.nlargest(limit, metric)
        else:
            return {
                'status': 'error',
                'message': f'Metric {metric} not found in data',
                'available_metrics': list(latest_data.columns)
            }
        
        # Format results
        results = []
        for _, row in top_markets.iterrows():
            results.append({
                'market_key': row['unique_key'],
                'loan_asset': row['loan_asset_symbol'],
                'collateral_asset': row['collateral_asset_symbol'],
                metric: float(row[metric]),
                'supply_apy': float(row['supply_apy']),
                'borrow_assets_usd': float(row['borrow_assets_usd']),
                'utilization': float(row['utilization'])
            })
        
        return {
            'status': 'success',
            'metric': metric,
            'limit': limit,
            'asset_filter': asset_filter,
            'time_range': f"{start_time.isoformat()} to {end_time.isoformat()}",
            'total_markets': len(latest_data),
            'top_markets': results
        }
    
    def compare_markets(self, market_keys: List[str], hours: int = 24) -> Dict:
        """Compare performance of multiple markets."""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)
        
        comparisons = {}
        
        for market_key in market_keys:
            data = self._get_market_data(market_key, start_time, end_time)
            
            if data:
                df = pd.DataFrame(data)
                df['timestamp'] = pd.to_datetime(df['query_timestamp'])
                df = df.sort_values('timestamp')
                
                # Calculate summary statistics
                comparisons[market_key] = {
                    'loan_asset': df.iloc[-1]['loan_asset_symbol'] if not df.empty else None,
                    'collateral_asset': df.iloc[-1]['collateral_asset_symbol'] if not df.empty else None,
                    'data_points': len(df),
                    'apy_stats': {
                        'current': float(df.iloc[-1]['supply_apy']) if not df.empty else None,
                        'average': float(df['supply_apy'].mean()),
                        'min': float(df['supply_apy'].min()),
                        'max': float(df['supply_apy'].max()),
                        'std': float(df['supply_apy'].std())
                    },
                    'borrowed_stats': {
                        'current': float(df.iloc[-1]['borrow_assets_usd']) if not df.empty else None,
                        'average': float(df['borrow_assets_usd'].mean()),
                        'min': float(df['borrow_assets_usd'].min()),
                        'max': float(df['borrow_assets_usd'].max())
                    },
                    'utilization_stats': {
                        'current': float(df.iloc[-1]['utilization']) if not df.empty else None,
                        'average': float(df['utilization'].mean()),
                        'min': float(df['utilization'].min()),
                        'max': float(df['utilization'].max())
                    }
                }
            else:
                comparisons[market_key] = {
                    'error': 'No data found'
                }
        
        return {
            'status': 'success',
            'time_range': f"{start_time.isoformat()} to {end_time.isoformat()}",
            'markets_compared': len(market_keys),
            'comparisons': comparisons
        }
    
    def _get_market_data(self, market_key: str, start_time: datetime, end_time: datetime) -> List[Dict]:
        """Get data for a specific market."""
        s3_objects = self._get_s3_objects_in_range(start_time, end_time)
        
        market_data = []
        
        for obj_key in s3_objects[:50]:  # Limit for performance
            try:
                obj = self.s3_client.get_object(Bucket=self.bucket, Key=obj_key)
                df = pd.read_parquet(obj['Body'])
                
                # Filter for specific market
                market_df = df[df['unique_key'] == market_key]
                
                if not market_df.empty:
                    file_timestamp = self._extract_timestamp_from_key(obj_key)
                    
                    for _, row in market_df.iterrows():
                        market_data.append({
                            'query_timestamp': file_timestamp.isoformat(),
                            'unique_key': row['unique_key'],
                            'loan_asset_symbol': row['loan_asset_symbol'],
                            'collateral_asset_symbol': row['collateral_asset_symbol'],
                            'supply_apy': float(row['supply_apy']),
                            'borrow_assets_usd': float(row['borrow_assets_usd']),
                            'utilization': float(row['utilization'])
                        })
                        
            except Exception as e:
                continue
        
        return market_data
    
    def _get_all_recent_data(self, start_time: datetime, end_time: datetime, asset_filter: str = None) -> List[Dict]:
        """Get all recent market data."""
        s3_objects = self._get_s3_objects_in_range(start_time, end_time)
        
        all_data = []
        
        for obj_key in s3_objects[:10]:  # Limit to most recent files
            try:
                obj = self.s3_client.get_object(Bucket=self.bucket, Key=obj_key)
                df = pd.read_parquet(obj['Body'])
                
                # Apply asset filter if specified
                if asset_filter:
                    df = df[
                        (df['loan_asset_symbol'] == asset_filter) |
                        (df['collateral_asset_symbol'] == asset_filter)
                    ]
                
                file_timestamp = self._extract_timestamp_from_key(obj_key)
                
                for _, row in df.iterrows():
                    all_data.append({
                        'query_timestamp': file_timestamp.isoformat(),
                        'unique_key': row['unique_key'],
                        'loan_asset_symbol': row['loan_asset_symbol'],
                        'collateral_asset_symbol': row['collateral_asset_symbol'],
                        'supply_apy': float(row['supply_apy']),
                        'borrow_assets_usd': float(row['borrow_assets_usd']),
                        'utilization': float(row['utilization'])
                    })
                    
            except Exception as e:
                continue
        
        return all_data
    
    def _get_s3_objects_in_range(self, start_time: datetime, end_time: datetime) -> List[str]:
        """Get S3 object keys for files in the specified time range."""
        objects = []
        
        current = start_time.replace(minute=0, second=0, microsecond=0)
        while current <= end_time:
            prefix = f"markets/year={current.year}/month={current.month:02d}/day={current.day:02d}/hour={current.hour:02d}/"
            
            try:
                response = self.s3_client.list_objects_v2(
                    Bucket=self.bucket,
                    Prefix=prefix
                )
                
                for obj in response.get('Contents', []):
                    file_time = self._extract_timestamp_from_key(obj['Key'])
                    if start_time <= file_time <= end_time:
                        objects.append(obj['Key'])
                        
            except Exception:
                continue
            
            current += timedelta(hours=1)
        
        return sorted(objects, reverse=True)  # Most recent first
    
    def _extract_timestamp_from_key(self, s3_key: str) -> datetime:
        """Extract timestamp from S3 key."""
        try:
            filename = s3_key.split('/')[-1]
            if '_' in filename:
                parts = filename.split('_')
                if len(parts) >= 3:
                    date_str = parts[1]
                    time_str = parts[2].split('.')[0]
                    timestamp_str = f"{date_str}_{time_str}"
                    return datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S').replace(tzinfo=timezone.utc)
        except:
            pass
        
        return datetime.now(timezone.utc)
    
    def _calculate_trends(self, df: pd.DataFrame) -> Dict:
        """Calculate trend metrics for a DataFrame."""
        if len(df) < 2:
            return {'error': 'Insufficient data for trend analysis'}
        
        # Calculate simple trends
        apy_trend = self._calculate_linear_trend(df['supply_apy'].values)
        borrowed_trend = self._calculate_linear_trend(df['borrow_assets_usd'].values)
        utilization_trend = self._calculate_linear_trend(df['utilization'].values)
        
        return {
            'apy_trend': {
                'slope': apy_trend,
                'direction': 'increasing' if apy_trend > 0.01 else 'decreasing' if apy_trend < -0.01 else 'stable',
                'start_value': float(df.iloc[0]['supply_apy']),
                'end_value': float(df.iloc[-1]['supply_apy']),
                'change_percent': ((float(df.iloc[-1]['supply_apy']) - float(df.iloc[0]['supply_apy'])) / float(df.iloc[0]['supply_apy'])) * 100 if float(df.iloc[0]['supply_apy']) != 0 else 0
            },
            'borrowed_trend': {
                'slope': borrowed_trend,
                'direction': 'increasing' if borrowed_trend > 100 else 'decreasing' if borrowed_trend < -100 else 'stable',
                'start_value': float(df.iloc[0]['borrow_assets_usd']),
                'end_value': float(df.iloc[-1]['borrow_assets_usd']),
                'change_percent': ((float(df.iloc[-1]['borrow_assets_usd']) - float(df.iloc[0]['borrow_assets_usd'])) / float(df.iloc[0]['borrow_assets_usd'])) * 100 if float(df.iloc[0]['borrow_assets_usd']) != 0 else 0
            },
            'utilization_trend': {
                'slope': utilization_trend,
                'direction': 'increasing' if utilization_trend > 0.01 else 'decreasing' if utilization_trend < -0.01 else 'stable',
                'start_value': float(df.iloc[0]['utilization']),
                'end_value': float(df.iloc[-1]['utilization']),
                'change_percent': ((float(df.iloc[-1]['utilization']) - float(df.iloc[0]['utilization'])) / float(df.iloc[0]['utilization'])) * 100 if float(df.iloc[0]['utilization']) != 0 else 0
            }
        }
    
    def _calculate_linear_trend(self, values: np.ndarray) -> float:
        """Calculate linear trend slope."""
        if len(values) < 2:
            return 0.0
        
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]
        return float(slope)


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description='Market analytics and trend analysis for Morpho data',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Commands:
  trends      Analyze trends for a specific market
  top         Get top performing markets
  compare     Compare multiple markets
  
Examples:
  # Analyze 24-hour trends for a specific market
  python market_analytics.py trends --market-key 0x123...abc --hours 24
  
  # Get top 10 markets by APY
  python market_analytics.py top --metric supply_apy --limit 10
  
  # Compare multiple markets
  python market_analytics.py compare --market-keys 0x123...abc 0x456...def --hours 12
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Trends command
    trends_parser = subparsers.add_parser('trends', help='Analyze market trends')
    trends_parser.add_argument('--market-key', required=True, help='Market unique key')
    trends_parser.add_argument('--hours', type=int, default=24, help='Time range in hours (default: 24)')
    
    # Top markets command
    top_parser = subparsers.add_parser('top', help='Get top performing markets')
    top_parser.add_argument('--metric', default='supply_apy', 
                           choices=['supply_apy', 'borrow_assets_usd', 'utilization'],
                           help='Metric to rank by (default: supply_apy)')
    top_parser.add_argument('--limit', type=int, default=10, help='Number of markets to return (default: 10)')
    top_parser.add_argument('--asset', help='Filter by asset symbol')
    top_parser.add_argument('--hours', type=int, default=1, help='Time range for recent data (default: 1)')
    
    # Compare command
    compare_parser = subparsers.add_parser('compare', help='Compare multiple markets')
    compare_parser.add_argument('--market-keys', nargs='+', required=True, help='Market unique keys to compare')
    compare_parser.add_argument('--hours', type=int, default=24, help='Time range in hours (default: 24)')
    
    # Global options
    parser.add_argument('--region', help='AWS region')
    parser.add_argument('--bucket', help='S3 bucket name')
    parser.add_argument('--pretty', action='store_true', help='Pretty print JSON output')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Initialize analytics client
    analytics = MarketAnalytics(region=args.region, bucket=args.bucket)
    
    # Execute command
    if args.command == 'trends':
        result = analytics.analyze_market_trends(args.market_key, args.hours)
    elif args.command == 'top':
        result = analytics.get_top_markets(args.metric, args.limit, args.asset, args.hours)
    elif args.command == 'compare':
        result = analytics.compare_markets(args.market_keys, args.hours)
    else:
        parser.error(f"Unknown command: {args.command}")
    
    # Output results
    if args.pretty:
        print(json.dumps(result, indent=2))
    else:
        print(json.dumps(result))


if __name__ == "__main__":
    main()
