"""
Data models for Morpho markets and related entities.
"""
from dataclasses import dataclass, asdict, field
from typing import Optional, Dict, Any, Union, List
from decimal import Decimal
import json
from datetime import datetime


@dataclass
class Asset:
    """Represents a crypto asset (loan or collateral)."""
    id: str
    symbol: str
    name: Optional[str] = None
    decimals: Optional[int] = None
    chain_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class MarketState:
    """Represents the current state of a market."""
    supply_apy: Optional[Union[float, str]] = None
    borrow_assets_usd: Optional[Union[float, str]] = None
    utilization: Optional[Union[float, str]] = None
    total_supply_assets: Optional[Union[float, str]] = None
    total_borrow_assets: Optional[Union[float, str]] = None
    last_updated: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        if self.last_updated:
            data['last_updated'] = self.last_updated.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MarketState':
        if 'last_updated' in data and data['last_updated']:
            data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        return cls(**data)


@dataclass
class Market:
    """Represents a Morpho lending market."""
    unique_key: str
    loan_asset: Asset
    collateral_asset: Asset
    state: MarketState
    whitelisted: bool = True
    chain_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'unique_key': self.unique_key,
            'loan_asset': self.loan_asset.to_dict(),
            'collateral_asset': self.collateral_asset.to_dict(),
            'state': self.state.to_dict(),
            'whitelisted': self.whitelisted,
            'chain_id': self.chain_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Market':
        return cls(
            unique_key=data['unique_key'],
            loan_asset=Asset(**data['loan_asset']),
            collateral_asset=Asset(**data['collateral_asset']),
            state=MarketState.from_dict(data['state']),
            whitelisted=data.get('whitelisted', True),
            chain_id=data.get('chain_id')
        )
    
    def to_json(self) -> str:
        return json.dumps(self.to_dict(), default=str)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Market':
        return cls.from_dict(json.loads(json_str))
    
    def get_display_name(self) -> str:
        """Get a human-readable market name."""
        collateral_symbol = self.collateral_asset.symbol or "UNKNOWN"
        loan_symbol = self.loan_asset.symbol or "UNKNOWN"
        return f"{collateral_symbol}/{loan_symbol}"
    
    def meets_minimum_borrowed(self, min_amount: float) -> bool:
        """Check if market meets minimum borrowed amount threshold."""
        if not self.state.borrow_assets_usd:
            return False
        try:
            borrow_amount = float(self.state.borrow_assets_usd)
            return borrow_amount >= min_amount
        except (ValueError, TypeError):
            return False
    
    def format_for_telegram(self) -> str:
        """Format market data for Telegram display."""
        supply_apy = "N/A"
        if self.state.supply_apy is not None:
            try:
                apy_value = float(self.state.supply_apy)
                supply_apy = f"{apy_value * 100:.2f}%"
            except (ValueError, TypeError):
                supply_apy = str(self.state.supply_apy)

        borrowed_usd = "N/A"
        if self.state.borrow_assets_usd is not None:
            try:
                borrowed_value = float(self.state.borrow_assets_usd)
                borrowed_usd = f"${borrowed_value:,.2f}"
            except (ValueError, TypeError):
                borrowed_usd = str(self.state.borrow_assets_usd)

        utilization = "N/A"
        if self.state.utilization is not None:
            try:
                util_value = float(self.state.utilization)
                utilization = f"{util_value * 100:.2f}%"
            except (ValueError, TypeError):
                utilization = str(self.state.utilization)

        chain_label = f"Chain {self.chain_id}" if self.chain_id is not None else "Chain N/A"

        return (f"• {chain_label} | {self.get_display_name()} | "
                f"Supply APY {supply_apy} | "
                f"Borrowed {borrowed_usd} | "
                f"Utilization {utilization} | "
                f"Key {self.unique_key}")


@dataclass
class VaultAllocation:
    """Represents a vault's allocation to a specific market."""
    market_unique_key: str
    allocated_amount: Optional[float] = None
    chain_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Vault:
    """Represents a Morpho vault."""
    address: str
    chain_id: Optional[int] = None
    chain_ids: Optional[List[int]] = None
    allocations: List[VaultAllocation] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'address': self.address,
            'chain_id': self.chain_id,
            'chain_ids': self.chain_ids,
            'allocations': [alloc.to_dict() for alloc in self.allocations]
        }
    
    def get_market_keys(self) -> set[str]:
        """Get set of unique market keys in this vault."""
        return {alloc.market_unique_key for alloc in self.allocations}
