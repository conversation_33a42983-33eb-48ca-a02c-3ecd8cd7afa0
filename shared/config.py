"""
Configuration management for the Morpho Helper application.
Supports both environment variables and AWS Secrets Manager.
"""
import os
import json
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """Application configuration."""
    # Morpho API settings
    morpho_api_url: str = "https://api.morpho.org/graphql"
    chain_id: int = 1
    chain_ids: List[int] = field(default_factory=lambda: [1])
    
    # Vault settings
    vault_address: Optional[str] = None
    minimal_borrowed_amount: float = 100000.0
    
    # Telegram settings
    telegram_token: Optional[str] = None
    
    # AWS settings
    aws_region: str = "us-east-1"
    
    # Database settings
    dynamodb_table_name: str = "morpho-markets"
    s3_bucket_name: str = "morpho-data-lake"
    redis_cluster_endpoint: Optional[str] = None
    
    # ETL settings
    etl_schedule_minutes: int = 60
    
    # Cache settings
    cache_ttl_seconds: int = 300  # 5 minutes
    
    @classmethod
    def from_env(cls) -> 'Config':
        """Load configuration from environment variables."""
        chain_ids_env = os.getenv("CHAIN_IDS")
        if chain_ids_env:
            parsed_chain_ids = [int(value.strip()) for value in chain_ids_env.split(',') if value.strip()]
        else:
            parsed_chain_ids = [int(os.getenv("CHAIN_ID", "1"))]

        # Ensure we always have at least one chain id and maintain backward compatibility
        if not parsed_chain_ids:
            parsed_chain_ids = [1]

        return cls(
            morpho_api_url=os.getenv("MORPHO_API_URL", "https://api.morpho.org/graphql"),
            chain_id=parsed_chain_ids[0],
            chain_ids=parsed_chain_ids,
            vault_address=os.getenv("VAULT_ADDRESS"),
            minimal_borrowed_amount=float(os.getenv("MINIMAL_BORROWED_AMOUNT", "100000")),
            telegram_token=os.getenv("TELEGRAM_TOKEN"),
            aws_region=os.getenv("AWS_REGION", "us-east-1"),
            dynamodb_table_name=os.getenv("DYNAMODB_TABLE_NAME", "morpho-markets"),
            s3_bucket_name=os.getenv("S3_BUCKET_NAME", "morpho-data-lake"),
            redis_cluster_endpoint=os.getenv("REDIS_CLUSTER_ENDPOINT"),
            etl_schedule_minutes=int(os.getenv("ETL_SCHEDULE_MINUTES", "60")),
            cache_ttl_seconds=int(os.getenv("CACHE_TTL_SECONDS", "300"))
        )
    
    def validate(self) -> bool:
        """Validate required configuration values."""
        required_fields = []
        
        # Check based on context (bot vs ETL)
        if os.getenv("SERVICE_TYPE") == "bot":
            required_fields = ["telegram_token", "redis_cluster_endpoint"]
        elif os.getenv("SERVICE_TYPE") == "etl":
            required_fields = ["vault_address", "dynamodb_table_name", "s3_bucket_name"]
        
        missing_fields = []
        for field in required_fields:
            if not getattr(self, field):
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"Missing required configuration: {missing_fields}")
            return False
        
        return True


def load_config() -> Config:
    """Load configuration from environment or AWS Secrets Manager."""
    config = Config.from_env()

    # Try to load from AWS Secrets Manager if running in AWS
    if os.getenv("AWS_EXECUTION_ENV"):
        try:
            import boto3
            secrets_client = boto3.client('secretsmanager', region_name=config.aws_region)

            # Load Telegram token from Secrets Manager
            if not config.telegram_token:
                try:
                    response = secrets_client.get_secret_value(SecretId='morpho-helper/telegram-token')
                    config.telegram_token = response['SecretString']
                    logger.info("Loaded Telegram token from AWS Secrets Manager")
                except Exception as e:
                    logger.warning(f"Could not load Telegram token from Secrets Manager: {e}")

            # Load app configuration from Secrets Manager
            try:
                app_config_secret = f"morpho-helper-{os.getenv('ENVIRONMENT', 'prod')}/app-config"
                response = secrets_client.get_secret_value(SecretId=app_config_secret)
                app_config = json.loads(response['SecretString'])

                # Update config with values from Secrets Manager if not already set
                if not config.vault_address and 'vault_address' in app_config:
                    config.vault_address = app_config['vault_address']
                    logger.info("Loaded vault address from AWS Secrets Manager")

                if 'minimal_borrowed_amount' in app_config:
                    config.minimal_borrowed_amount = float(app_config['minimal_borrowed_amount'])
                    logger.info("Loaded minimal borrowed amount from AWS Secrets Manager")

            except Exception as e:
                logger.warning(f"Could not load app config from Secrets Manager: {e}")

        except ImportError:
            logger.warning("boto3 not available, skipping AWS Secrets Manager")
        except Exception as e:
            logger.warning(f"Error loading from AWS Secrets Manager: {e}")

    if not config.validate():
        raise ValueError("Invalid configuration")

    return config


# Global config instance
config = load_config()
