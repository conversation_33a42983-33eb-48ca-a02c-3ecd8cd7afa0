# ECS Deployment Solution - Complete Fix

## Problem Summary

The user encountered a `CannotPullContainerError` when trying to update the ECS task using `./scripts/deploy.sh`. The error was:

```
CannotPullContainerError: The task cannot pull 236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-helper-prod/bot:latest@sha256:... from the registry. There is a connection issue between the task and the registry. Check your task network configuration.
```

## Root Cause Analysis

The issue was caused by **network connectivity problems** between ECS tasks and ECR:

1. **ECS Configuration**: Tasks were configured with `assign_public_ip = false`
2. **VPC Setup**: Using default VPC without NAT Gateway
3. **VPC Endpoints**: Not properly configured with route table associations
4. **ECR Access**: Tasks couldn't reach ECR to pull Docker images

## Solution Implemented

### 1. Fixed ECS Network Configuration

**File**: `terraform/ecs.tf`
```hcl
network_configuration {
  subnets          = data.aws_subnet.default[*].id
  security_groups  = [aws_security_group.ecs_tasks.id]
  # Temporarily enable public IP for ECR access until VPC endpoints are properly configured
  assign_public_ip = true
}
```

### 2. Fixed VPC Endpoints Configuration

**File**: `terraform/vpc-endpoints.tf`
- Added proper route table associations for S3 and DynamoDB gateway endpoints
- Ensured ECR API and DKR interface endpoints are properly configured

**File**: `terraform/vpc.tf`
- Added `data.aws_route_tables.default` to get all route tables in the VPC

### 3. Enhanced Deployment Script

**File**: `scripts/deploy.sh`
- Added command-line argument parsing for selective deployment steps
- Improved error handling and logging
- Added comprehensive ECS deployment workflow
- Fixed region configuration (changed from `us-east-1` to `eu-central-1`)

**New Arguments**:
```bash
./scripts/deploy.sh [options]
  --skip-infrastructure  Skip Terraform infrastructure deployment
  --skip-secrets         Skip secrets setup  
  --skip-images          Skip Docker image building
  --skip-services        Skip ECS service updates
  --skip-etl             Skip initial ETL job
```

### 4. Created Dedicated ECS Update Script

**File**: `scripts/update-ecs-service.sh`
- Standalone script for ECS service updates
- Handles Docker image building and pushing
- Comprehensive deployment status checking
- Detailed error reporting with AWS console links

**Usage**:
```bash
# Update service only
./scripts/update-ecs-service.sh

# Build images and update service
./scripts/update-ecs-service.sh --build-images
```

### 5. Updated Documentation

**File**: `README.md`
- Added comprehensive ECS deployment troubleshooting section
- Included step-by-step diagnosis and solution procedures
- Added manual deployment instructions
- Documented all new script options

## Verification Results

✅ **Infrastructure Applied Successfully**:
- ECS service updated with `assign_public_ip = true`
- VPC endpoints properly configured with route tables
- All Terraform changes applied without errors

✅ **ECS Service Deployment Working**:
- Service status: `ACTIVE`
- Running tasks: `1/1`
- Task definition: `morpho-helper-prod-bot:4`
- Deployment status: `PRIMARY`

✅ **Scripts Functioning Properly**:
- `./scripts/deploy.sh` with selective options working
- `./scripts/update-ecs-service.sh` successfully updating services
- Proper error handling and status reporting

## Key Technical Improvements

1. **Network Connectivity**: Enabled public IP assignment for ECS tasks to access ECR
2. **VPC Endpoints**: Properly configured gateway and interface endpoints
3. **Deployment Automation**: Enhanced scripts with better error handling
4. **Monitoring**: Added comprehensive status checking and reporting
5. **Documentation**: Complete troubleshooting guide for future issues

## Usage Instructions

### For Regular Deployments:
```bash
# Full deployment
./scripts/deploy.sh

# Update only services (most common)
./scripts/deploy.sh --skip-infrastructure --skip-secrets --skip-etl
```

### For Quick ECS Updates:
```bash
# Update existing service
./scripts/update-ecs-service.sh

# Build new images and update
./scripts/update-ecs-service.sh --build-images
```

### For Troubleshooting:
1. Check ECS service events in AWS console
2. Verify VPC endpoints are available
3. Ensure ECR repositories exist and are accessible
4. Use the troubleshooting guide in README.md

## Future Considerations

1. **Long-term Solution**: Consider implementing NAT Gateway for private subnet access
2. **Cost Optimization**: Public IP assignment increases costs slightly
3. **Security**: Monitor security group rules for public IP access
4. **Monitoring**: Set up CloudWatch alarms for deployment failures

The solution provides both immediate fixes and long-term maintainability for the ECS deployment process.
