"""
Morpho API client with enhanced error handling and data transformation.
"""
import requests
import json
import logging
import time
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from models.market import Market, Asset, MarketState, Vault, VaultAllocation
from shared.config import config

logger = logging.getLogger(__name__)


class MorphoAPIClient:
    """Client for interacting with the Morpho GraphQL API."""
    
    def __init__(self, api_url: str = None):
        self.api_url = api_url or config.morpho_api_url
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})
        self.chain_ids = config.chain_ids
    
    def _run_query(self, query: str, variables: Optional[Dict[str, Any]] = None, max_retries: int = 3) -> Dict[str, Any]:
        """Execute a GraphQL query with enhanced error handling and retry logic."""
        data = {"query": query}
        if variables:
            data["variables"] = variables

        for attempt in range(max_retries):
            try:
                logger.info(f"Sending GraphQL query to {self.api_url} (attempt {attempt + 1}/{max_retries})")
                response = self.session.post(self.api_url, json=data, timeout=30)
                response.raise_for_status()

                result = response.json()

                # Check for GraphQL errors
                if 'errors' in result:
                    logger.error(f"GraphQL errors in response: {result['errors']}")
                    raise ValueError(f"GraphQL errors: {result['errors']}")

                if 'data' not in result:
                    logger.warning("No 'data' field in GraphQL response")
                    raise ValueError("Invalid GraphQL response: missing 'data' field")

                return result

            except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Network error on attempt {attempt + 1}: {e}. Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Network error after {max_retries} attempts: {e}")
                    raise
            except requests.exceptions.HTTPError as e:
                if e.response.status_code >= 500 and attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    logger.warning(f"Server error on attempt {attempt + 1}: {e}. Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"HTTP error when calling Morpho API: {e}")
                    raise
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON response from Morpho API: {e}")
                raise ValueError(f"Invalid JSON response: {e}")
            except Exception as e:
                logger.error(f"Unexpected error when calling Morpho API: {e}")
                raise
    
    def get_asset_ids_by_symbol(self, symbol: str) -> List[str]:
        """Get all asset IDs for a given symbol."""
        query = """
        query GetAssetIdBySymbol($assetSymbol: String!, $chainIds: [Int!]) {
          assets(where: { symbol_in: [$assetSymbol] chainId_in: $chainIds}) {
            items {
              id
              symbol
              chainId
            }
          }
        }
        """
        
        try:
            logger.info(f"Fetching asset IDs for symbol: {symbol}")
            variables = {
                "assetSymbol": symbol,
                "chainIds": self.chain_ids
            }
            result = self._run_query(query, variables)
            
            assets = result.get('data', {}).get('assets', {}).get('items', [])
            asset_ids = [asset['id'] for asset in assets if isinstance(asset, dict) and 'id' in asset]
            
            logger.info(f"Found {len(asset_ids)} asset IDs for symbol {symbol}")
            return asset_ids
            
        except Exception as e:
            logger.error(f"Error fetching asset IDs for symbol {symbol}: {e}")
            return []
    
    def get_vault_markets(self, vault_address: str) -> Optional[Vault]:
        """Get vault information including market allocations."""
        query = """
        query GetMarketsInVault($vaultAddress: String!, $chainId: Int!) {
          vaultByAddress(address: $vaultAddress, chainId: $chainId) {
            chainId
            state {
              allocation {
                market {
                  uniqueKey
                  chainId
                }
              }
            }
          }
        }
        """
        
        try:
            logger.info(f"Fetching markets for vault: {vault_address}")
            allocations: List[VaultAllocation] = []
            discovered_chain_ids: List[int] = []

            for chain_id in self.chain_ids:
                variables = {"vaultAddress": vault_address, "chainId": chain_id}
                result = self._run_query(query, variables)

                vault_data = result.get('data', {}).get('vaultByAddress')
                if not vault_data:
                    logger.info(f"Vault not found for address {vault_address} on chain {chain_id}")
                    continue

                discovered_chain_ids.append(chain_id)

                allocation_data = vault_data.get('state', {}).get('allocation', [])

                for item in allocation_data:
                    if isinstance(item, dict) and 'market' in item:
                        market = item['market']
                        if isinstance(market, dict) and 'uniqueKey' in market:
                            allocations.append(VaultAllocation(
                                market_unique_key=market['uniqueKey'],
                                chain_id=market.get('chainId', chain_id)
                            ))

            if not discovered_chain_ids:
                logger.warning(f"Vault not found for address: {vault_address} on any configured chain ({self.chain_ids})")
                return None

            vault = Vault(
                address=vault_address,
                chain_id=discovered_chain_ids[0] if len(discovered_chain_ids) == 1 else None,
                chain_ids=discovered_chain_ids,
                allocations=allocations
            )

            logger.info(f"Found {len(allocations)} markets in vault across chains {discovered_chain_ids}")
            return vault
            
        except Exception as e:
            logger.error(f"Error fetching vault markets: {e}")
            return None
    
    def get_whitelisted_markets(self,
                              loan_symbols: Optional[List[str]] = None,
                              collateral_symbols: Optional[List[str]] = None) -> List[Market]:
        """Get whitelisted markets with optional filtering and pagination."""
        query = """
        query GetAllWhitelistedMarkets(
          $loanId: [String!]
          $collateralId: [String!]
          $chainIds: [Int!]
          $first: Int
          $skip: Int
        ) {
          markets(
            where: {
              whitelisted: true
              loanAssetId_in: $loanId
              collateralAssetId_in: $collateralId
              chainId_in: $chainIds
            }
            first: $first
            skip: $skip
          ) {
            items {
              uniqueKey
              chainId
              state {
                supplyApy
                borrowAssetsUsd
                utilization
                supplyAssets
                borrowAssets
              }
              loanAsset {
                id
                symbol
                name
                decimals
                chainId
              }
              collateralAsset {
                id
                symbol
                name
                decimals
                chainId
              }
            }
          }
        }
        """
        
        try:
            base_variables = {
                'chainIds': self.chain_ids
            }

            # Resolve loan symbols to IDs
            if loan_symbols:
                loan_ids = []
                for symbol in loan_symbols:
                    ids = self.get_asset_ids_by_symbol(symbol)
                    loan_ids.extend(ids)
                if loan_ids:
                    base_variables['loanId'] = loan_ids
                    logger.info(f"Filtering by loan asset IDs: {loan_ids}")

            # Resolve collateral symbols to IDs
            if collateral_symbols:
                collateral_ids = []
                for symbol in collateral_symbols:
                    ids = self.get_asset_ids_by_symbol(symbol)
                    collateral_ids.extend(ids)
                if collateral_ids:
                    base_variables['collateralId'] = collateral_ids
                    logger.info(f"Filtering by collateral asset IDs: {collateral_ids}")

            # Implement pagination to get all markets
            all_markets = []
            page_size = 1000  # Morpho API typical limit
            skip = 0

            while True:
                page_variables = dict(base_variables)
                page_variables['first'] = page_size
                page_variables['skip'] = skip

                logger.info(f"Fetching whitelisted markets (page {skip // page_size + 1}, skip: {skip})")
                result = self._run_query(query, page_variables)

                markets_data = result.get('data', {}).get('markets', {}).get('items', [])

                if not markets_data:
                    break

                page_markets = []
                for market_data in markets_data:
                    try:
                        market = self._parse_market_data(market_data)
                        if market:
                            page_markets.append(market)
                    except Exception as e:
                        logger.warning(f"Error parsing market data: {e}")
                        continue

                all_markets.extend(page_markets)

                # If we got fewer items than page_size, we've reached the end
                if len(markets_data) < page_size:
                    break

                skip += page_size

            logger.info(f"Found {len(all_markets)} valid whitelisted markets across all pages")
            return all_markets
            
        except Exception as e:
            logger.error(f"Error fetching whitelisted markets: {e}")
            return []
    
    def _parse_market_data(self, market_data: Dict[str, Any]) -> Optional[Market]:
        """Parse raw market data from API into Market object."""
        if not isinstance(market_data, dict) or 'uniqueKey' not in market_data:
            return None
        
        # Parse loan asset
        loan_asset_data = market_data.get('loanAsset') or {}
        if not isinstance(loan_asset_data, dict):
            return None
        loan_asset = Asset(
            id=loan_asset_data.get('id', ''),
            symbol=loan_asset_data.get('symbol', 'UNKNOWN'),
            name=loan_asset_data.get('name'),
            decimals=loan_asset_data.get('decimals'),
            chain_id=loan_asset_data.get('chainId')
        )

        # Parse collateral asset
        collateral_asset_data = market_data.get('collateralAsset') or {}
        if not isinstance(collateral_asset_data, dict):
            return None
        collateral_asset = Asset(
            id=collateral_asset_data.get('id', ''),
            symbol=collateral_asset_data.get('symbol', 'UNKNOWN'),
            name=collateral_asset_data.get('name'),
            decimals=collateral_asset_data.get('decimals'),
            chain_id=collateral_asset_data.get('chainId')
        )
        
        # Parse market state with proper type conversion
        state_data = market_data.get('state', {})

        # Helper function to safely convert string numbers to float
        def safe_float_convert(value):
            if value is None:
                return None
            if isinstance(value, str):
                try:
                    return float(value)
                except (ValueError, OverflowError):
                    logger.warning(f"Could not convert '{value}' to float, keeping as string")
                    return value
            return value

        market_state = MarketState(
            supply_apy=safe_float_convert(state_data.get('supplyApy')),
            borrow_assets_usd=safe_float_convert(state_data.get('borrowAssetsUsd')),
            utilization=safe_float_convert(state_data.get('utilization')),
            total_supply_assets=safe_float_convert(state_data.get('supplyAssets')),
            total_borrow_assets=safe_float_convert(state_data.get('borrowAssets')),
            last_updated=datetime.now(timezone.utc)
        )
        
        return Market(
            unique_key=market_data['uniqueKey'],
            loan_asset=loan_asset,
            collateral_asset=collateral_asset,
            state=market_state,
            whitelisted=True,
            chain_id=market_data.get('chainId')
        )
