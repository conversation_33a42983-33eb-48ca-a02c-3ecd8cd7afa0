"""
Telegram bot command handlers.
"""
import logging
from telegram import Update
from telegram.ext import ContextTypes

from bot.services import MarketService

logger = logging.getLogger(__name__)


class BotHandlers:
    """Telegram bot command handlers."""
    
    def __init__(self, market_service: MarketService):
        self.market_service = market_service
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Main command handler for the telegram bot.

        Usage:
        /start - Get all new whitelisted markets
        /start [loan_asset] - Filter by loan asset only
        /start [loan_asset] [collateral_asset] - Filter by both assets
        /start _ [collateral_asset] - Filter by collateral asset only (use _ as placeholder)

        Examples:
        /start USDC
        /start USDC WETH
        /start _ WETH
        """
        try:
            args = context.args if hasattr(context, "args") else []

            # Parse arguments with support for placeholder
            loan_asset = None
            collateral_asset = None

            if len(args) > 0:
                # If first argument is not a placeholder, use it as loan asset
                if args[0] != "_":
                    loan_asset = args[0]

            if len(args) > 1:
                # Second argument is always collateral asset
                collateral_asset = args[1]

            # Get markets from cache/database (returns list of messages for pagination)
            messages = await self.market_service.get_new_whitelisted_markets(
                loan_asset_filter=loan_asset,
                collateral_asset_filter=collateral_asset
            )

            # Send each message sequentially
            for i, message in enumerate(messages):
                if i > 0:
                    # Add a small delay between messages to avoid rate limiting
                    import asyncio
                    await asyncio.sleep(0.5)
                await update.message.reply_text(message)
            
        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text(
                "❌ An error occurred while fetching market data. Please try again later."
            )

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Provides help information about bot usage.
        """
        help_text = """🤖 *Morpho Markets Bot Help*

This bot helps you find new whitelisted markets not yet in your vault.

*Commands:*
• /start - Get all new whitelisted markets
• /start \\[loan\\_asset\\] - Filter by loan asset only
• /start \\[loan\\_asset\\] \\[collateral\\_asset\\] - Filter by both assets
• /start \\_ \\[collateral\\_asset\\] - Filter by collateral asset only (use \\_ as placeholder)
• /help - Show this help message

*Examples:*
• /start - All new markets
• /start USDC - Markets with USDC as loan asset
• /start USDC WETH - Markets with USDC loan and WETH collateral
• /start \\_ WETH - Markets with WETH as collateral asset

*Market Information Displayed:*
• Asset pair (loan/collateral)
• Supply APY
• Total borrowed amount (USD)
• Utilization rate
• Unique market key

*Data Source:*
Market data is updated every hour and served from a high-performance cache for fast responses."""
        await update.message.reply_text(help_text, parse_mode='Markdown')
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        Show bot status and data freshness.
        """
        try:
            status_info = await self.market_service.get_status_info()
            await update.message.reply_text(status_info, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in status command: {e}")
            await update.message.reply_text(
                "❌ Unable to retrieve status information."
            )
