"""
Bot business logic and data access services.
"""
import logging
from typing import Optional, List
import datetime

from models.market import Market
from shared.storage import RedisCache, DynamoDBStorage
from shared.config import config

logger = logging.getLogger(__name__)


class MarketService:
    """Service for market data operations in the bot."""
    
    def __init__(self):
        self.redis_cache = RedisCache()
        self.dynamodb_storage = DynamoDBStorage()
    
    async def get_new_whitelisted_markets(self,
                                        loan_asset_filter: Optional[str] = None,
                                        collateral_asset_filter: Optional[str] = None) -> List[str]:
        """
        Get formatted list of messages for new whitelisted markets not in vault.
        Implements robust fallback mechanism to guarantee data availability.
        Returns a list of messages for pagination support.
        """
        try:
            markets = await self._get_markets_with_fallback(loan_asset_filter, collateral_asset_filter)

            if not markets:
                return ["❌ No market data available. All fallback mechanisms exhausted. Please contact support."]

            # Filter by vault (this should be cached too, but for now we'll use config)
            vault_address = config.vault_address
            if not vault_address:
                return ["❌ No vault address configured."]

            # For now, we'll assume all markets from cache/DB are already filtered
            # In a full implementation, we'd also cache vault allocations
            filtered_markets = self._filter_by_minimum_borrowed(markets)

            if not filtered_markets:
                return ["✅ No new whitelisted markets found for your vault with the given filters."]

            return self._format_markets_for_telegram(filtered_markets)

        except Exception as e:
            logger.error(f"Error getting new whitelisted markets: {e}")
            return ["❌ An error occurred while fetching market data. Please try again later."]

    async def _get_markets_with_fallback(self,
                                       loan_asset_filter: Optional[str] = None,
                                       collateral_asset_filter: Optional[str] = None) -> List[Market]:
        """
        Robust 3-tier fallback mechanism to guarantee market data availability.
        Tries multiple data sources in order of preference:
        1. Redis cache (fastest)
        2. DynamoDB LATEST records (filtered)
        3. DynamoDB ALL records (unfiltered, with deduplication)
        """
        # Fallback 1: Redis cache (fastest)
        try:
            markets = self.redis_cache.get_cached_markets(
                loan_symbol=loan_asset_filter,
                collateral_symbol=collateral_asset_filter
            )
            if markets:
                logger.info(f"Retrieved {len(markets)} markets from Redis cache")
                return markets
        except Exception as e:
            logger.warning(f"Redis cache failed: {e}")

        # Fallback 2: DynamoDB LATEST records (filtered)
        try:
            logger.info("Cache miss, falling back to DynamoDB LATEST records")
            markets = self.dynamodb_storage.get_markets_by_assets(
                loan_symbol=loan_asset_filter,
                collateral_symbol=collateral_asset_filter
            )
            if markets:
                logger.info(f"Retrieved {len(markets)} markets from DynamoDB LATEST")
                return markets
        except Exception as e:
            logger.warning(f"DynamoDB LATEST query failed: {e}")

        # Fallback 3: DynamoDB ALL records (unfiltered, with deduplication)
        try:
            logger.info("DynamoDB LATEST failed, falling back to ALL records")
            markets = self.dynamodb_storage.get_all_latest_markets()
            if markets:
                # Apply asset filters manually
                filtered_markets = self._apply_asset_filters(markets, loan_asset_filter, collateral_asset_filter)
                logger.info(f"Retrieved {len(filtered_markets)} markets from DynamoDB ALL (filtered)")
                return filtered_markets
        except Exception as e:
            logger.warning(f"DynamoDB ALL query failed: {e}")

        logger.error("All fallback mechanisms exhausted - no market data available")
        return []

    def _apply_asset_filters(self, markets: List[Market],
                           loan_symbol: Optional[str] = None,
                           collateral_symbol: Optional[str] = None) -> List[Market]:
        """Apply asset symbol filters to a list of markets."""
        filtered = markets

        if loan_symbol:
            filtered = [m for m in filtered if m.loan_asset.symbol == loan_symbol]

        if collateral_symbol:
            filtered = [m for m in filtered if m.collateral_asset.symbol == collateral_symbol]

        return filtered

    def _filter_by_minimum_borrowed(self, markets: List[Market]) -> List[Market]:
        """Filter markets by minimum borrowed amount and deduplicate."""
        # First deduplicate by unique_key (safety measure)
        unique_markets = {}
        for market in markets:
            if market.unique_key not in unique_markets:
                unique_markets[market.unique_key] = market
            else:
                logger.warning(f"Duplicate market detected and skipped: {market.unique_key}")

        # Then filter by minimum borrowed amount
        return [
            market for market in unique_markets.values()
            if market.meets_minimum_borrowed(config.minimal_borrowed_amount)
        ]
    
    def _format_markets_for_telegram(self, markets: List[Market]) -> List[str]:
        """
        Format markets for Telegram display with pagination support.
        Returns a list of messages, each under Telegram's character limit.
        """
        if not markets:
            return ["✅ No new whitelisted markets found for your vault with the given filters."]

        # Sort by supply APY (descending)
        sorted_markets = sorted(
            markets,
            key=lambda m: m.state.supply_apy or -1,
            reverse=True
        )

        # Calculate pagination
        max_chars_per_message = 3500  # Safety margin under 4096 limit
        messages = []
        current_message_lines = []
        current_length = 0

        # Header for first message
        unique_chains = sorted({market.chain_id for market in markets if market.chain_id is not None})
        if unique_chains:
            chain_summary = ", ".join(str(chain) for chain in unique_chains)
            header = f"✅ New whitelisted markets not yet in your vault ({len(markets)} total | chains: {chain_summary})"
        else:
            header = f"✅ New whitelisted markets not yet in your vault ({len(markets)} total)"
        current_message_lines.append(header)
        current_length = len(header) + 1  # +1 for newline

        for i, market in enumerate(sorted_markets):
            market_line = market.format_for_telegram()
            line_length = len(market_line) + 1  # +1 for newline

            # Check if adding this line would exceed the limit
            if current_length + line_length > max_chars_per_message and current_message_lines:
                # Finalize current message
                messages.append("\n".join(current_message_lines))

                # Start new message
                page_num = len(messages) + 1
                continuation_header = f"📄 Continued (Page {page_num}):"
                current_message_lines = [continuation_header]
                current_length = len(continuation_header) + 1

            # Add the market line
            current_message_lines.append(market_line)
            current_length += line_length

        # Add the final message if there are remaining lines
        if current_message_lines:
            messages.append("\n".join(current_message_lines))

        # Add page indicators if multiple messages
        if len(messages) > 1:
            for i in range(len(messages)):
                page_indicator = f"\n\n📊 Page {i + 1} of {len(messages)}"
                messages[i] += page_indicator

        return messages
    
    async def get_status_info(self) -> str:
        """Get bot status and data freshness information."""
        try:
            # Check Redis connectivity
            redis_status = "✅ Connected"
            try:
                self.redis_cache.redis_client.ping()
            except Exception:
                redis_status = "❌ Disconnected"
            
            # Check DynamoDB connectivity
            dynamodb_status = "✅ Connected"
            try:
                # Test DynamoDB connectivity by trying to query for markets
                # This avoids the DescribeTable permission issue
                test_markets = self.dynamodb_storage.get_all_latest_markets()
                if not test_markets:
                    dynamodb_status = "⚠️ Connected but no data"
            except Exception as e:
                logger.error(f"DynamoDB connectivity test failed: {e}")
                dynamodb_status = "❌ Disconnected"
            
            # Get sample market to check data freshness using fallback mechanism
            sample_markets = await self._get_markets_with_fallback()
            data_freshness = "❌ No data available"
            cache_count = len(self.redis_cache.get_cached_markets())

            if sample_markets:
                latest_update = max(
                    market.state.last_updated for market in sample_markets
                    if market.state.last_updated
                )
                if latest_update:
                    time_diff = datetime.datetime.now(datetime.timezone.utc) - latest_update
                    hours_old = time_diff.total_seconds() / 3600
                    data_freshness = f"✅ {hours_old:.1f} hours old"
            
            status_text = f"""📊 *Bot Status*

*Cache (Redis):* {redis_status}
*Database (DynamoDB):* {dynamodb_status}
*Data Freshness:* {data_freshness}
*Markets in Cache:* {cache_count}
*Total Markets Available:* {len(sample_markets)}

*Configuration:*
• Vault Address: `{config.vault_address[:10]}...{config.vault_address[-6:]}`
• Min Borrowed Amount: ${config.minimal_borrowed_amount:,.0f}
• Cache TTL: {config.cache_ttl_seconds} seconds"""
            
            return status_text.strip()
            
        except Exception as e:
            logger.error(f"Error getting status info: {e}")
            return "❌ Unable to retrieve status information."
