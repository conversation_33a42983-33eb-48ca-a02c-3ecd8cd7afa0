#!/bin/bash

# Fix ElastiCache Subnet Group Issue
# This script fixes the "SubnetInUse" error when updating ElastiCache subnet groups

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "Starting ElastiCache subnet group fix..."

# Check if we're in the right directory
if [ ! -f "terraform/elasticache.tf" ]; then
    log_error "This script must be run from the project root directory"
    exit 1
fi

# Backup current state
log_info "Creating backup of current Terraform state..."
cd terraform
cp terraform.tfstate terraform.tfstate.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# Show what will be changed
log_info "Showing planned changes..."
terraform plan

echo
log_warn "This will replace the Redis cluster and subnet group to fix the subnet issue."
log_warn "The Redis cluster will be recreated with a new endpoint."
log_warn "This will cause temporary downtime for the Redis cache."
echo

read -p "Do you want to proceed with the fix? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_warn "Fix cancelled by user."
    exit 1
fi

# Apply the fix
log_info "Applying the ElastiCache subnet group fix..."
terraform apply -auto-approve

log_info "Fix completed successfully!"
log_info "The Redis cluster has been recreated with the correct subnet configuration."

# Show new Redis endpoint
NEW_ENDPOINT=$(terraform output -raw redis_endpoint)
log_info "New Redis endpoint: ${NEW_ENDPOINT}"

echo
log_info "Next steps:"
echo "1. Update any hardcoded Redis endpoints in your applications"
echo "2. Restart your bot service to pick up the new Redis endpoint"
echo "3. Run an ETL job to repopulate the Redis cache"

cd ..
